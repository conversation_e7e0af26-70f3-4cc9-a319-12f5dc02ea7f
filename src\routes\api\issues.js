import { Hono } from 'hono';
import { Issue } from '../../models/Issue.js';

export const issueApiRoutes = new Hono();

// POST /api/issues - Create a new issue
issueApiRoutes.post('/', async (c) => {
  try {
    const body = await c.req.json();
    
    // Validate required fields
    const { machine_id, operator_number, title } = body;
    
    if (!machine_id || !operator_number || !title) {
      return c.json({ 
        error: 'Missing required fields: machine_id, operator_number, title' 
      }, 400);
    }

    const issue = await Issue.create(body);
    return c.json(issue, 201);
  } catch (error) {
    console.error('Error creating issue:', error);
    return c.json({ error: 'Failed to create issue' }, 500);
  }
});

// GET /api/issues - Get all issues with optional filtering
issueApiRoutes.get('/', async (c) => {
  try {
    const query = c.req.query();
    const filters = {};
    
    // Parse query parameters for filtering
    if (query.machine_id) {
      filters.machine_id = parseInt(query.machine_id);
    }
    
    if (query.status) {
      filters.status = query.status;
    }
    
    if (query.priority) {
      filters.priority = query.priority;
    }

    const issues = await Issue.findAll(filters);
    return c.json(issues);
  } catch (error) {
    console.error('Error fetching issues:', error);
    return c.json({ error: 'Failed to fetch issues' }, 500);
  }
});

// GET /api/issues/:id - Get issue by ID
issueApiRoutes.get('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const issue = await Issue.findById(id);
    
    if (!issue) {
      return c.json({ error: 'Issue not found' }, 404);
    }
    
    return c.json(issue);
  } catch (error) {
    console.error('Error fetching issue:', error);
    return c.json({ error: 'Failed to fetch issue' }, 500);
  }
});

// PUT /api/issues/:id - Update issue
issueApiRoutes.put('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const updateData = await c.req.json();
    
    const issue = await Issue.update(id, updateData);
    
    if (!issue) {
      return c.json({ error: 'Issue not found' }, 404);
    }
    
    return c.json(issue);
  } catch (error) {
    console.error('Error updating issue:', error);
    return c.json({ error: 'Failed to update issue' }, 500);
  }
});

// DELETE /api/issues/:id - Delete issue
issueApiRoutes.delete('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const deleted = await Issue.delete(id);
    
    if (!deleted) {
      return c.json({ error: 'Issue not found' }, 404);
    }
    
    return c.json({ message: 'Issue deleted successfully' });
  } catch (error) {
    console.error('Error deleting issue:', error);
    return c.json({ error: 'Failed to delete issue' }, 500);
  }
});

// GET /api/issues/statistics - Get issue statistics
issueApiRoutes.get('/statistics', async (c) => {
  try {
    const stats = await Issue.getStatistics();
    return c.json(stats);
  } catch (error) {
    console.error('Error fetching issue statistics:', error);
    return c.json({ error: 'Failed to fetch statistics' }, 500);
  }
});
