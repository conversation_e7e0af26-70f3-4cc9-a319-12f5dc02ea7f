import { Hono } from 'hono';
import { html } from 'hono/html';
import { Machine } from '../models/Machine.js';
import { Issue } from '../models/Issue.js';
import { MaintenanceRequest } from '../models/MaintenanceRequest.js';
import { generateQRCodeDataURL } from '../utils/qrGenerator.js';

export const pageRoutes = new Hono();

// Root redirect to admin
pageRoutes.get('/', (c) => {
  return c.redirect('/admin');
});

// Admin dashboard
pageRoutes.get('/admin', async (c) => {
  try {
    const machines = await Machine.findAll();
    const issueStats = await Issue.getStatistics();
    const recentIssues = await Issue.findAll({ limit: 5 });
    const maintenanceStats = await MaintenanceRequest.getStatistics();
    const recentMaintenance = await MaintenanceRequest.findAll({ limit: 5 });

    const machinesHtml = machines.map(machine => `
      <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
        <h3 class="font-semibold">${machine.name}</h3>
        <p class="text-gray-600">${machine.machine_number}</p>
        <p class="text-sm text-gray-500">${machine.location || 'Asukoht määramata'}</p>
        <div class="mt-2">
          <span class="inline-block px-2 py-1 text-xs rounded ${
            machine.status === 'online' ? 'bg-green-100 text-green-800' :
            machine.status === 'offline' ? 'bg-red-100 text-red-800' :
            'bg-yellow-100 text-yellow-800'
          }">
            ${machine.status}
          </span>
        </div>
        <a href="/machines/${machine.id}"
           class="inline-block mt-2 text-blue-500 hover:text-blue-600">
          Vaata detaile →
        </a>
      </div>
    `).join('');

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Administraatori töölaud</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          <h1 class="text-3xl font-bold text-gray-800 mb-8">CMMS Administraatori töölaud</h1>

          <!-- Issue Statistics -->
          <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">📋 Rikete ülevaade</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                  📋
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Kokku rikked</p>
                  <p class="text-2xl font-semibold text-gray-900">${issueStats.total_issues || 0}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                  🚨
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Avatud rikked</p>
                  <p class="text-2xl font-semibold text-gray-900">${issueStats.open_issues || 0}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                  🔧
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Töös</p>
                  <p class="text-2xl font-semibold text-gray-900">${issueStats.in_progress_issues || 0}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                  ⚠️
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Kriitilised</p>
                  <p class="text-2xl font-semibold text-gray-900">${issueStats.critical_issues || 0}</p>
                </div>
              </div>
            </div>
          </div>
          </div>

          <!-- Maintenance Statistics -->
          <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">🔧 Hoolduse ülevaade</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                  🔧
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Kokku taotlused</p>
                  <p class="text-2xl font-semibold text-gray-900">${maintenanceStats.total_requests || 0}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                  ⏳
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Ootel</p>
                  <p class="text-2xl font-semibold text-gray-900">${maintenanceStats.pending_requests || 0}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                  📅
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Planeeritud</p>
                  <p class="text-2xl font-semibold text-gray-900">${maintenanceStats.scheduled_requests || 0}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                  🚨
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Kiireloomulised</p>
                  <p class="text-2xl font-semibold text-gray-900">${maintenanceStats.urgent_requests || 0}</p>
                </div>
              </div>
            </div>
          </div>
          </div>

          <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-xl font-semibold">Masinad</h2>
              <div class="flex flex-wrap gap-2">
                <button onclick="regenerateAllQRCodes()"
                        class="bg-orange-500 hover:bg-orange-600 text-white px-3 py-2 rounded text-sm">
                  🔄 Uuenda QR
                </button>
                <a href="/machines/new"
                   data-testid="add-machine-btn"
                   class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-sm">
                  ➕ Lisa masin
                </a>
                <a href="/machines"
                   class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm">
                  📋 Kõik masinad
                </a>
                <a href="/admin/issues"
                   class="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded text-sm">
                  🚨 Halda rikked
                </a>
                <a href="/admin/maintenance"
                   class="bg-orange-600 hover:bg-orange-700 text-white px-3 py-2 rounded text-sm">
                  🔧 Halda hooldus
                </a>
                <a href="/admin/partners"
                   class="bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded text-sm">
                  🤝 Partnerid
                </a>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              ${machinesHtml}
            </div>
          </div>
        </div>

        <script>
          async function regenerateAllQRCodes() {
            const button = event.target;
            const originalText = button.textContent;

            button.disabled = true;
            button.textContent = 'Uuendamine...';
            button.className = 'bg-gray-500 text-white px-4 py-2 rounded text-sm cursor-not-allowed';

            try {
              const response = await fetch('/api/machines/regenerate-all-qr', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                }
              });

              const result = await response.json();

              if (response.ok) {
                alert('QR koodid edukalt uuendatud: ' + result.message);
                // Refresh page to show updated QR codes
                window.location.reload();
              } else {
                alert('Viga QR koodide uuendamisel: ' + result.error);
              }
            } catch (error) {
              alert('Viga QR koodide uuendamisel: ' + error.message);
            } finally {
              button.disabled = false;
              button.textContent = originalText;
              button.className = 'bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded text-sm';
            }
          }
        </script>
      </body>
      </html>
    `;

    return new Response(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });
  } catch (error) {
    console.error('Error loading admin dashboard:', error);
    return c.text('Error loading dashboard', 500);
  }
});

// Machine list
pageRoutes.get('/machines', async (c) => {
  try {
    const machines = await Machine.findAll();

    const machinesTableRows = machines.map(machine => `
      <tr>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
          ${machine.machine_number}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          ${machine.name}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${machine.location || '-'}
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            machine.status === 'online' ? 'bg-green-100 text-green-800' :
            machine.status === 'offline' ? 'bg-red-100 text-red-800' :
            'bg-yellow-100 text-yellow-800'
          }">
            ${machine.status}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <a href="/machines/${machine.id}" class="text-blue-600 hover:text-blue-900">
            Vaata
          </a>
        </td>
      </tr>
    `).join('');

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Masinad</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">Masinad</h1>
            <a href="/admin" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">
              ← Tagasi töölauale
            </a>
          </div>

          <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <table class="w-full">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Masina number
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Nimi
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Asukoht
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Staatus
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Toimingud
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                ${machinesTableRows}
              </tbody>
            </table>
          </div>
        </div>
      </body>
      </html>
    `;

    return new Response(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });
  } catch (error) {
    console.error('Error loading machines:', error);
    return c.text('Error loading machines', 500);
  }
});

// New machine form
pageRoutes.get('/machines/new', (c) => {
  return c.html(html`
    <!DOCTYPE html>
    <html lang="et">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>CMMS - Lisa uus masin</title>
      <script src="https://cdn.tailwindcss.com"></script>
    </head>
    <body class="bg-gray-100">
      <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">Lisa uus masin</h1>

        <div class="bg-white rounded-lg shadow-md p-6 max-w-2xl">
          <form action="/machines" method="POST" class="space-y-6">
            <div>
              <label for="machine_number" class="block text-sm font-medium text-gray-700">
                Masina number *
              </label>
              <input type="text"
                     name="machine_number"
                     id="machine_number"
                     required
                     class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
            </div>

            <div>
              <label for="name" class="block text-sm font-medium text-gray-700">
                Nimi *
              </label>
              <input type="text"
                     name="name"
                     id="name"
                     required
                     class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="manufacturer" class="block text-sm font-medium text-gray-700">
                  Tootja
                </label>
                <input type="text"
                       name="manufacturer"
                       id="manufacturer"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>

              <div>
                <label for="model" class="block text-sm font-medium text-gray-700">
                  Mudel
                </label>
                <input type="text"
                       name="model"
                       id="model"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="department" class="block text-sm font-medium text-gray-700">
                  Osakond
                </label>
                <input type="text"
                       name="department"
                       id="department"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>

              <div>
                <label for="location" class="block text-sm font-medium text-gray-700">
                  Asukoht
                </label>
                <input type="text"
                       name="location"
                       id="location"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>
            </div>

            <div class="flex justify-end space-x-4">
              <a href="/admin"
                 class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                Tühista
              </a>
              <button type="submit"
                      class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                Lisa masin
              </button>
            </div>
          </form>
        </div>
      </div>
    </body>
    </html>
  `);
});

// Handle form submission for new machine
pageRoutes.post('/machines', async (c) => {
  try {
    const body = await c.req.parseBody();

    // Convert FormData to object
    const machineData = {};
    for (const [key, value] of Object.entries(body)) {
      machineData[key] = value;
    }

    const machine = await Machine.create(machineData);

    // Redirect to machine detail view with success message
    return c.redirect(`/machines/${machine.id}?success=created`);
  } catch (error) {
    console.error('Error creating machine:', error);

    // In a real app, we'd show the form again with error message
    return c.text(`Error creating machine: ${error.message}`, 500);
  }
});

// Operator view - mobile-friendly interface accessed via QR code
pageRoutes.get('/operator/:machineNumber', async (c) => {
  try {
    const machineNumber = c.req.param('machineNumber');
    const machine = await Machine.findByMachineNumber(machineNumber);

    if (!machine) {
      return c.html(html`
        <!DOCTYPE html>
        <html lang="et">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>CMMS - Masinat ei leitud</title>
          <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100 min-h-screen flex items-center justify-center">
          <div class="max-w-md w-full mx-4">
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
              <div class="alert-error bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                Masinat ei leitud
              </div>
              <div data-testid="error-message" class="text-gray-600 mb-4">
                Masina number "${machineNumber}" ei ole süsteemis registreeritud.
              </div>
              <a href="/admin" class="inline-block bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                Tagasi administraatori vaatesse
              </a>
            </div>
          </div>
        </body>
        </html>
      `, 404);
    }

    return c.html(html`
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - ${machine.name}</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
          /* Ensure mobile-friendly touch targets */
          button, input[type="submit"], input[type="button"] {
            min-height: 44px;
          }
        </style>
      </head>
      <body class="bg-gray-100 min-h-screen">
        <div class="container max-w-md mx-auto px-4 py-6">
          <!-- Machine Information -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div data-testid="machine-info" class="text-center mb-4">
              <h1 class="text-2xl font-bold text-gray-800 mb-2">
                <span data-testid="machine-name">${machine.name}</span>
              </h1>
              <p class="text-lg text-gray-600 mb-2">
                <span data-testid="machine-number">${machine.machine_number}</span>
              </p>
              <div class="flex justify-center items-center space-x-4 text-sm">
                <span data-testid="machine-status" class="inline-flex px-3 py-1 rounded-full text-xs font-semibold ${
                  machine.status === 'online' ? 'bg-green-100 text-green-800' :
                  machine.status === 'offline' ? 'bg-red-100 text-red-800' :
                  'bg-yellow-100 text-yellow-800'
                }">
                  ${machine.status}
                </span>
                ${machine.location ? html`
                  <span data-testid="machine-location" class="text-gray-500">
                    📍 ${machine.location}
                  </span>
                ` : ''}
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="space-y-4">
            <a href="/operator/${machine.machine_number}/issue"
               data-testid="report-issue-btn"
               class="block w-full bg-red-500 hover:bg-red-600 text-white text-center py-4 px-6 rounded-lg font-semibold text-lg">
              🚨 Teata rikest
            </a>

            <a href="/operator/${machine.machine_number}/maintenance"
               data-testid="report-maintenance-btn"
               class="block w-full bg-orange-500 hover:bg-orange-600 text-white text-center py-4 px-6 rounded-lg font-semibold text-lg">
              🔧 Teata hoolduse vajadusest
            </a>
          </div>

          <!-- Operator Number Input (for future issue reporting) -->
          <div class="mt-8 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold mb-4">Operaatori andmed</h2>
            <form class="space-y-4">
              <div>
                <label for="operator_number" class="block text-sm font-medium text-gray-700 mb-2">
                  Operaatori number
                </label>
                <input type="text"
                       name="operator_number"
                       id="operator_number"
                       placeholder="OP-123"
                       class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-lg"
                       autofocus>
              </div>
            </form>
          </div>

          <!-- Footer -->
          <div class="mt-8 text-center text-sm text-gray-500">
            <p>CMMS - Computerized Maintenance Management System</p>
          </div>
        </div>

        <script>
          // Progressive enhancement: Auto-focus first input
          document.addEventListener('DOMContentLoaded', function() {
            const operatorInput = document.querySelector('[name="operator_number"]');
            if (operatorInput) {
              operatorInput.focus();
            }
          });
        </script>
      </body>
      </html>
    `);
  } catch (error) {
    console.error('Error loading operator view:', error);
    return c.text('Error loading operator view', 500);
  }
});

// Machine detail view
pageRoutes.get('/machines/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const machine = await Machine.findById(id);

    if (!machine) {
      return c.text('Machine not found', 404);
    }

    const success = c.req.query('success');
    const qrCodeDataURL = await generateQRCodeDataURL(machine.machine_number);

    return c.html(html`
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - ${machine.name}</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          ${success === 'created' ? html`
            <div class="alert-success bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
              Masin edukalt lisatud
            </div>
          ` : success === 'updated' ? html`
            <div class="alert-success bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
              Andmed edukalt uuendatud
            </div>
          ` : ''}

          <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">
              <span data-testid="machine-name">${machine.name}</span>
              <span class="text-gray-500 text-lg">
                (<span data-testid="machine-number">${machine.machine_number}</span>)
              </span>
            </h1>
            <div class="space-x-2">
              <a href="/machines/${machine.id}/edit"
                 data-testid="edit-machine"
                 class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded inline-block">
                Muuda andmeid
              </a>
              <button onclick="deleteMachine(${machine.id}, '${machine.machine_number}')"
                      data-testid="delete-machine"
                      class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
                Kustuta masin
              </button>
            </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Machine details -->
            <div class="lg:col-span-2">
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Masina andmed</h2>
                <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Tootja</dt>
                    <dd class="mt-1 text-sm text-gray-900">${machine.manufacturer || '-'}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Mudel</dt>
                    <dd class="mt-1 text-sm text-gray-900">${machine.model || '-'}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Osakond</dt>
                    <dd class="mt-1 text-sm text-gray-900">${machine.department || '-'}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Asukoht</dt>
                    <dd class="mt-1 text-sm text-gray-900">${machine.location || '-'}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Staatus</dt>
                    <dd class="mt-1">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        machine.status === 'online' ? 'bg-green-100 text-green-800' :
                        machine.status === 'offline' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }">
                        ${machine.status}
                      </span>
                    </dd>
                  </div>
                </dl>
              </div>
            </div>

            <!-- QR Code -->
            <div class="lg:col-span-1">
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">QR-kood</h2>
                <div class="text-center">
                  <img src="${qrCodeDataURL}"
                       alt="QR kood masinale ${machine.machine_number}"
                       data-testid="qr-code"
                       class="mx-auto mb-4 border rounded">
                  <p class="text-sm text-gray-600 mb-4">
                    Skaneeri QR-kood operaatori vaate avamiseks
                  </p>
                  <a href="/api/files/qr/${machine.id}"
                     download="machine_${machine.machine_number}_qr.png"
                     data-testid="download-qr"
                     class="inline-block bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded text-sm">
                    Laadi alla PNG
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <script>
          async function deleteMachine(machineId, machineNumber) {
            // Double confirmation for safety
            const firstConfirm = confirm(
              \`Kas olete kindel, et soovite kustutada masina "\${machineNumber}"?\\n\\n\` +
              'See tegevus on pöördumatu ja kustutab ka kõik seotud andmed (QR kood, hooldusajalugu jne).'
            );

            if (!firstConfirm) {
              return;
            }

            const secondConfirm = confirm(
              \`VIIMANE HOIATUS!\\n\\n\` +
              \`Masin "\${machineNumber}" kustutatakse jäädavalt.\\n\\n\` +
              'Kas olete täiesti kindel?'
            );

            if (!secondConfirm) {
              return;
            }

            try {
              const response = await fetch(\`/api/machines/\${machineId}\`, {
                method: 'DELETE',
                headers: {
                  'Content-Type': 'application/json'
                }
              });

              const result = await response.json();

              if (response.ok) {
                alert(\`Masin "\${machineNumber}" edukalt kustutatud.\`);
                // Redirect to admin dashboard
                window.location.href = '/admin';
              } else {
                alert('Viga masina kustutamisel: ' + result.error);
              }
            } catch (error) {
              alert('Viga masina kustutamisel: ' + error.message);
            }
          }
        </script>
      </body>
      </html>
    `);
  } catch (error) {
    console.error('Error loading machine:', error);
    return c.text('Error loading machine', 500);
  }
});

// Machine edit form
pageRoutes.get('/machines/:id/edit', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const machine = await Machine.findById(id);

    if (!machine) {
      return c.text('Machine not found', 404);
    }

    return c.html(html`
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Muuda masinat: ${machine.name}</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          <h1 class="text-3xl font-bold text-gray-800 mb-8">Muuda masinat: ${machine.name}</h1>

          <div class="bg-white rounded-lg shadow-md p-6 max-w-2xl">
            <form action="/machines/${machine.id}" method="POST" class="space-y-6">
              <input type="hidden" name="_method" value="PUT">

              <div>
                <label for="machine_number" class="block text-sm font-medium text-gray-700">
                  Masina number *
                </label>
                <input type="text"
                       name="machine_number"
                       id="machine_number"
                       value="${machine.machine_number}"
                       required
                       readonly
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500">
                <p class="text-xs text-gray-500 mt-1">Masina numbrit ei saa muuta</p>
              </div>

              <div>
                <label for="name" class="block text-sm font-medium text-gray-700">
                  Nimi *
                </label>
                <input type="text"
                       name="name"
                       id="name"
                       value="${machine.name}"
                       required
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="manufacturer" class="block text-sm font-medium text-gray-700">
                    Tootja
                  </label>
                  <input type="text"
                         name="manufacturer"
                         id="manufacturer"
                         value="${machine.manufacturer || ''}"
                         class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                  <label for="model" class="block text-sm font-medium text-gray-700">
                    Mudel
                  </label>
                  <input type="text"
                         name="model"
                         id="model"
                         value="${machine.model || ''}"
                         class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="department" class="block text-sm font-medium text-gray-700">
                    Osakond
                  </label>
                  <input type="text"
                         name="department"
                         id="department"
                         value="${machine.department || ''}"
                         class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                  <label for="location" class="block text-sm font-medium text-gray-700">
                    Asukoht
                  </label>
                  <input type="text"
                         name="location"
                         id="location"
                         value="${machine.location || ''}"
                         class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>
              </div>

              <div>
                <label for="status" class="block text-sm font-medium text-gray-700">
                  Staatus
                </label>
                <select name="status"
                        id="status"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                  <option value="online" ${machine.status === 'online' ? 'selected' : ''}>Online</option>
                  <option value="offline" ${machine.status === 'offline' ? 'selected' : ''}>Offline</option>
                  <option value="maintenance" ${machine.status === 'maintenance' ? 'selected' : ''}>Hoolduses</option>
                </select>
              </div>

              <div class="flex justify-end space-x-4">
                <a href="/machines/${machine.id}"
                   class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                  Tühista
                </a>
                <button type="submit"
                        class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                  Salvesta muudatused
                </button>
              </div>
            </form>
          </div>
        </div>
      </body>
      </html>
    `);
  } catch (error) {
    console.error('Error loading machine edit form:', error);
    return c.text('Error loading edit form', 500);
  }
});

// Handle machine update
pageRoutes.post('/machines/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const body = await c.req.parseBody();

    // Convert FormData to object, excluding _method
    const updateData = {};
    for (const [key, value] of Object.entries(body)) {
      if (key !== '_method') {
        updateData[key] = value;
      }
    }

    const machine = await Machine.update(id, updateData);

    if (!machine) {
      return c.text('Machine not found', 404);
    }

    // Redirect to machine detail view with success message
    return c.redirect(`/machines/${machine.id}?success=updated`);
  } catch (error) {
    console.error('Error updating machine:', error);
    return c.text(`Error updating machine: ${error.message}`, 500);
  }
});

// Handle machine deletion (DELETE request from frontend)
pageRoutes.delete('/machines/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const deleted = await Machine.delete(id);

    if (!deleted) {
      return c.json({ error: 'Machine not found' }, 404);
    }

    return c.json({ message: 'Machine deleted successfully' });
  } catch (error) {
    console.error('Error deleting machine:', error);
    return c.json({ error: 'Failed to delete machine' }, 500);
  }
});

// Issue reporting form for operators
pageRoutes.get('/operator/:machineNumber/issue', async (c) => {
  try {
    const machineNumber = c.req.param('machineNumber');
    const machine = await Machine.findByMachineNumber(machineNumber);

    if (!machine) {
      return c.html(`
        <!DOCTYPE html>
        <html lang="et">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>CMMS - Masinat ei leitud</title>
          <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100 min-h-screen flex items-center justify-center">
          <div class="max-w-md w-full mx-4">
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
              <div class="alert-error bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                Masinat ei leitud
              </div>
              <div class="text-gray-600 mb-4">
                Masina number "${machineNumber}" ei ole süsteemis registreeritud.
              </div>
              <a href="/admin" class="inline-block bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                Tagasi administraatori vaatesse
              </a>
            </div>
          </div>
        </body>
        </html>
      `, 404);
    }

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Teata rikest: ${machine.name}</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
          /* Ensure mobile-friendly touch targets */
          button, input[type="submit"], input[type="button"], select {
            min-height: 44px;
          }
        </style>
      </head>
      <body class="bg-gray-100 min-h-screen">
        <div class="container max-w-2xl mx-auto px-4 py-6">
          <!-- Header -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">🚨 Teata rikest</h1>
            <div class="text-gray-600">
              <p><strong>Masin:</strong> ${machine.name}</p>
              <p><strong>Number:</strong> ${machine.machine_number}</p>
              <p><strong>Asukoht:</strong> ${machine.location || 'Määramata'}</p>
            </div>
          </div>

          <!-- Issue Form -->
          <div class="bg-white rounded-lg shadow-md p-6">
            <form data-testid="issue-form" id="issueForm" class="space-y-6">
              <input type="hidden" name="machine_id" value="${machine.id}">

              <!-- Operator Information -->
              <div class="border-b pb-4">
                <h2 class="text-lg font-semibold mb-4">Operaatori andmed</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label for="operator_number" class="block text-sm font-medium text-gray-700 mb-2">
                      Operaatori number *
                    </label>
                    <input type="text"
                           name="operator_number"
                           id="operator_number"
                           required
                           placeholder="OP-123"
                           class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                  </div>
                  <div>
                    <label for="operator_name" class="block text-sm font-medium text-gray-700 mb-2">
                      Operaatori nimi
                    </label>
                    <input type="text"
                           name="operator_name"
                           id="operator_name"
                           placeholder="Nimi Perekonnanimi"
                           class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                  </div>
                </div>
              </div>

              <!-- Issue Details -->
              <div>
                <h2 class="text-lg font-semibold mb-4">Rikke andmed</h2>
                <div class="space-y-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label for="issue_type" class="block text-sm font-medium text-gray-700 mb-2">
                        Rikke tüüp *
                      </label>
                      <select name="issue_type"
                              id="issue_type"
                              required
                              class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                        <option value="">Vali rikke tüüp</option>
                        <option value="mechanical">🔧 Mehaaniline</option>
                        <option value="electrical">⚡ Elektriline</option>
                        <option value="software">💻 Tarkvara</option>
                        <option value="safety">⚠️ Ohutus</option>
                        <option value="quality">✅ Kvaliteet</option>
                        <option value="other">❓ Muu</option>
                      </select>
                    </div>
                    <div>
                      <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
                        Prioriteet *
                      </label>
                      <select name="priority"
                              id="priority"
                              required
                              class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                        <option value="">Vali prioriteet</option>
                        <option value="low">🟢 Madal</option>
                        <option value="medium" selected>🟡 Keskmine</option>
                        <option value="high">🟠 Kõrge</option>
                        <option value="critical">🔴 Kriitiline</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                      Rikke pealkiri *
                    </label>
                    <input type="text"
                           name="title"
                           id="title"
                           required
                           placeholder="Lühike rikke kirjeldus"
                           maxlength="200"
                           class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                  </div>

                  <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                      Detailne kirjeldus
                    </label>
                    <textarea name="description"
                              id="description"
                              rows="4"
                              placeholder="Kirjelda rikke sümptomeid, millal see tekkis, mis tingimuste all jne..."
                              class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500"></textarea>
                  </div>
                </div>
              </div>

              <!-- Error/Success Messages -->
              <div id="messageContainer" class="hidden">
                <div id="errorMessage" class="error-message bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 hidden"></div>
                <div id="successMessage" class="success-message bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 hidden"></div>
              </div>

              <!-- Submit Button -->
              <div class="flex flex-col sm:flex-row gap-4">
                <button type="submit"
                        data-testid="submit-issue"
                        class="flex-1 bg-red-500 hover:bg-red-600 text-white py-4 px-6 rounded-lg font-semibold text-lg">
                  🚨 Saada rike teatis
                </button>
                <a href="/operator/${machine.machine_number}"
                   class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-4 px-6 rounded-lg font-semibold text-lg text-center">
                  ← Tagasi
                </a>
              </div>
            </form>
          </div>
        </div>

        <script>
          document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('issueForm');
            const messageContainer = document.getElementById('messageContainer');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');

            // Auto-focus first input
            const operatorNumberInput = document.querySelector('[name="operator_number"]');
            if (operatorNumberInput) {
              operatorNumberInput.focus();
            }

            form.addEventListener('submit', async function(e) {
              e.preventDefault();

              // Hide previous messages
              messageContainer.classList.add('hidden');
              errorMessage.classList.add('hidden');
              successMessage.classList.add('hidden');

              // Get form data
              const formData = new FormData(form);
              const issueData = Object.fromEntries(formData.entries());

              // Basic validation
              if (!issueData.operator_number || !issueData.issue_type || !issueData.priority || !issueData.title) {
                showError('Palun täida kõik kohustuslikud väljad (*)');
                return;
              }

              try {
                const response = await fetch('/api/issues', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify(issueData)
                });

                const result = await response.json();

                if (response.ok) {
                  showSuccess('Rike teatis edukalt saadetud! Hooldusmeeskond on teavitatud.');
                  form.reset();
                  // Redirect after 2 seconds
                  setTimeout(() => {
                    window.location.href = '/operator/${machine.machine_number}';
                  }, 2000);
                } else {
                  showError('Viga rike teatamisel: ' + (result.error || 'Tundmatu viga'));
                }
              } catch (error) {
                showError('Viga rike teatamisel: ' + error.message);
              }
            });

            function showError(message) {
              errorMessage.textContent = message;
              errorMessage.classList.remove('hidden');
              messageContainer.classList.remove('hidden');
              window.scrollTo({ top: 0, behavior: 'smooth' });
            }

            function showSuccess(message) {
              successMessage.textContent = message;
              successMessage.classList.remove('hidden');
              messageContainer.classList.remove('hidden');
              window.scrollTo({ top: 0, behavior: 'smooth' });
            }
          });
        </script>
      </body>
      </html>
    `;

    return new Response(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });
  } catch (error) {
    console.error('Error loading issue form:', error);
    return c.text('Error loading issue form', 500);
  }
});

// Maintenance reporting form for operators
pageRoutes.get('/operator/:machineNumber/maintenance', async (c) => {
  try {
    const machineNumber = c.req.param('machineNumber');
    const machine = await Machine.findByMachineNumber(machineNumber);

    if (!machine) {
      return c.html(`
        <!DOCTYPE html>
        <html lang="et">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>CMMS - Masinat ei leitud</title>
          <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100 min-h-screen flex items-center justify-center">
          <div class="max-w-md w-full mx-4">
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
              <div class="alert-error bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                Masinat ei leitud
              </div>
              <div class="text-gray-600 mb-4">
                Masina number "${machineNumber}" ei ole süsteemis registreeritud.
              </div>
              <a href="/admin" class="inline-block bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                Tagasi administraatori vaatesse
              </a>
            </div>
          </div>
        </body>
        </html>
      `, 404);
    }

    // Get today's date for default requested_date
    const today = new Date().toISOString().split('T')[0];

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Teata hoolduse vajadusest: ${machine.name}</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
          /* Ensure mobile-friendly touch targets */
          button, input[type="submit"], input[type="button"], select {
            min-height: 44px;
          }
        </style>
      </head>
      <body class="bg-gray-100 min-h-screen">
        <div class="container max-w-2xl mx-auto px-4 py-6">
          <!-- Header -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">🔧 Teata hoolduse vajadusest</h1>
            <div class="text-gray-600">
              <p><strong>Masin:</strong> ${machine.name}</p>
              <p><strong>Number:</strong> ${machine.machine_number}</p>
              <p><strong>Asukoht:</strong> ${machine.location || 'Määramata'}</p>
            </div>
          </div>

          <!-- Maintenance Form -->
          <div class="bg-white rounded-lg shadow-md p-6">
            <form data-testid="maintenance-form" id="maintenanceForm" class="space-y-6">
              <input type="hidden" name="machine_id" value="${machine.id}">

              <!-- Operator Information -->
              <div class="border-b pb-4">
                <h2 class="text-lg font-semibold mb-4">Operaatori andmed</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label for="operator_number" class="block text-sm font-medium text-gray-700 mb-2">
                      Operaatori number *
                    </label>
                    <input type="text"
                           name="operator_number"
                           id="operator_number"
                           required
                           placeholder="OP-123"
                           class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                  </div>
                  <div>
                    <label for="operator_name" class="block text-sm font-medium text-gray-700 mb-2">
                      Operaatori nimi
                    </label>
                    <input type="text"
                           name="operator_name"
                           id="operator_name"
                           placeholder="Nimi Perekonnanimi"
                           class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                  </div>
                </div>
              </div>

              <!-- Maintenance Details -->
              <div>
                <h2 class="text-lg font-semibold mb-4">Hoolduse andmed</h2>
                <div class="space-y-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label for="maintenance_type" class="block text-sm font-medium text-gray-700 mb-2">
                        Hoolduse tüüp *
                      </label>
                      <select name="maintenance_type"
                              id="maintenance_type"
                              required
                              class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                        <option value="">Vali hoolduse tüüp</option>
                        <option value="preventive">🔧 Ennetav hooldus</option>
                        <option value="corrective">🛠️ Parandav hooldus</option>
                        <option value="emergency">🚨 Hädahooldus</option>
                        <option value="inspection">🔍 Kontroll/Inspektsioon</option>
                        <option value="calibration">⚖️ Kalibreerimine</option>
                        <option value="cleaning">🧽 Puhastamine</option>
                        <option value="other">❓ Muu</option>
                      </select>
                    </div>
                    <div>
                      <label for="urgency" class="block text-sm font-medium text-gray-700 mb-2">
                        Kiireloomulisus *
                      </label>
                      <select name="urgency"
                              id="urgency"
                              required
                              class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                        <option value="">Vali kiireloomulisus</option>
                        <option value="low">🟢 Madal</option>
                        <option value="medium" selected>🟡 Keskmine</option>
                        <option value="high">🟠 Kõrge</option>
                        <option value="urgent">🔴 Kiire</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                      Hoolduse pealkiri *
                    </label>
                    <input type="text"
                           name="title"
                           id="title"
                           required
                           placeholder="Lühike hoolduse kirjeldus"
                           maxlength="200"
                           class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                  </div>

                  <div>
                    <label for="requested_date" class="block text-sm font-medium text-gray-700 mb-2">
                      Soovitud kuupäev
                    </label>
                    <input type="date"
                           name="requested_date"
                           id="requested_date"
                           value="${today}"
                           class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                  </div>

                  <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                      Detailne kirjeldus
                    </label>
                    <textarea name="description"
                              id="description"
                              rows="4"
                              placeholder="Kirjelda hoolduse vajadust, märgatud probleeme, viimast hooldust jne..."
                              class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500"></textarea>
                  </div>
                </div>
              </div>

              <!-- Error/Success Messages -->
              <div id="messageContainer" class="hidden">
                <div id="errorMessage" class="error-message bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 hidden"></div>
                <div id="successMessage" class="success-message bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 hidden"></div>
              </div>

              <!-- Submit Button -->
              <div class="flex flex-col sm:flex-row gap-4">
                <button type="submit"
                        data-testid="submit-maintenance"
                        class="flex-1 bg-orange-500 hover:bg-orange-600 text-white py-4 px-6 rounded-lg font-semibold text-lg">
                  🔧 Saada hoolduse taotlus
                </button>
                <a href="/operator/${machine.machine_number}"
                   class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-4 px-6 rounded-lg font-semibold text-lg text-center">
                  ← Tagasi
                </a>
              </div>
            </form>
          </div>
        </div>

        <script>
          document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('maintenanceForm');
            const messageContainer = document.getElementById('messageContainer');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');

            // Auto-focus first input
            const operatorNumberInput = document.querySelector('[name="operator_number"]');
            if (operatorNumberInput) {
              operatorNumberInput.focus();
            }

            form.addEventListener('submit', async function(e) {
              e.preventDefault();

              // Hide previous messages
              messageContainer.classList.add('hidden');
              errorMessage.classList.add('hidden');
              successMessage.classList.add('hidden');

              // Get form data
              const formData = new FormData(form);
              const maintenanceData = Object.fromEntries(formData.entries());

              // Basic validation
              if (!maintenanceData.operator_number || !maintenanceData.maintenance_type || !maintenanceData.urgency || !maintenanceData.title) {
                showError('Palun täida kõik kohustuslikud väljad (*)');
                return;
              }

              try {
                const response = await fetch('/api/maintenance', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify(maintenanceData)
                });

                const result = await response.json();

                if (response.ok) {
                  showSuccess('Hoolduse taotlus edukalt saadetud! Hooldusmeeskond on teavitatud.');
                  form.reset();
                  // Reset date to today
                  document.getElementById('requested_date').value = '${today}';
                  // Redirect after 2 seconds
                  setTimeout(() => {
                    window.location.href = '/operator/${machine.machine_number}';
                  }, 2000);
                } else {
                  showError('Viga hoolduse taotlemisel: ' + (result.error || 'Tundmatu viga'));
                }
              } catch (error) {
                showError('Viga hoolduse taotlemisel: ' + error.message);
              }
            });

            function showError(message) {
              errorMessage.textContent = message;
              errorMessage.classList.remove('hidden');
              messageContainer.classList.remove('hidden');
              window.scrollTo({ top: 0, behavior: 'smooth' });
            }

            function showSuccess(message) {
              successMessage.textContent = message;
              successMessage.classList.remove('hidden');
              messageContainer.classList.remove('hidden');
              window.scrollTo({ top: 0, behavior: 'smooth' });
            }
          });
        </script>
      </body>
      </html>
    `;

    return new Response(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });
  } catch (error) {
    console.error('Error loading maintenance form:', error);
    return c.text('Error loading maintenance form', 500);
  }
});

// Issues management page for administrators
pageRoutes.get('/admin/issues', async (c) => {
  try {
    const query = c.req.query();
    const filters = {};

    // Parse query parameters for filtering
    if (query.status) filters.status = query.status;
    if (query.priority) filters.priority = query.priority;
    if (query.machine_id) filters.machine_id = parseInt(query.machine_id);

    const issues = await Issue.findAll(filters);
    const machines = await Machine.findAll();
    const issueStats = await Issue.getStatistics();

    const issuesTableRows = issues.map(issue => `
      <tr class="hover:bg-gray-50">
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
          #${issue.id}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          ${issue.machine_number}
        </td>
        <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
          ${issue.title}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${issue.operator_number}
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            issue.priority === 'critical' ? 'bg-red-100 text-red-800' :
            issue.priority === 'high' ? 'bg-orange-100 text-orange-800' :
            issue.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
            'bg-green-100 text-green-800'
          }">
            ${issue.priority}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            issue.status === 'open' ? 'bg-red-100 text-red-800' :
            issue.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' :
            issue.status === 'resolved' ? 'bg-green-100 text-green-800' :
            'bg-gray-100 text-gray-800'
          }">
            ${issue.status}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${new Date(issue.reported_at).toLocaleDateString('et-EE')}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <a href="/admin/issues/${issue.id}" class="text-blue-600 hover:text-blue-900 mr-2">
            Vaata
          </a>
          <button onclick="updateIssueStatus(${issue.id}, '${issue.status}')"
                  class="text-green-600 hover:text-green-900">
            Muuda
          </button>
        </td>
      </tr>
    `).join('');

    const machineOptions = machines.map(machine => `
      <option value="${machine.id}" ${filters.machine_id === machine.id ? 'selected' : ''}>
        ${machine.machine_number} - ${machine.name}
      </option>
    `).join('');

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Rikete haldamine</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          <!-- Header -->
          <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">🚨 Rikete haldamine</h1>
            <a href="/admin" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">
              ← Tagasi töölauale
            </a>
          </div>

          <!-- Statistics -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">📋</div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Kokku rikked</p>
                  <p class="text-2xl font-semibold text-gray-900">${issueStats.total_issues || 0}</p>
                </div>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">🚨</div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Avatud</p>
                  <p class="text-2xl font-semibold text-gray-900">${issueStats.open_issues || 0}</p>
                </div>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">🔧</div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Töös</p>
                  <p class="text-2xl font-semibold text-gray-900">${issueStats.in_progress_issues || 0}</p>
                </div>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100 text-orange-600">⚠️</div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Kriitilised</p>
                  <p class="text-2xl font-semibold text-gray-900">${issueStats.critical_issues || 0}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Filters -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold mb-4">Filtrid</h2>
            <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Staatus</label>
                <select name="status" id="status" class="w-full border-gray-300 rounded-md shadow-sm">
                  <option value="">Kõik staatused</option>
                  <option value="open" ${filters.status === 'open' ? 'selected' : ''}>Avatud</option>
                  <option value="in_progress" ${filters.status === 'in_progress' ? 'selected' : ''}>Töös</option>
                  <option value="resolved" ${filters.status === 'resolved' ? 'selected' : ''}>Lahendatud</option>
                  <option value="closed" ${filters.status === 'closed' ? 'selected' : ''}>Suletud</option>
                </select>
              </div>
              <div>
                <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">Prioriteet</label>
                <select name="priority" id="priority" class="w-full border-gray-300 rounded-md shadow-sm">
                  <option value="">Kõik prioriteedid</option>
                  <option value="low" ${filters.priority === 'low' ? 'selected' : ''}>Madal</option>
                  <option value="medium" ${filters.priority === 'medium' ? 'selected' : ''}>Keskmine</option>
                  <option value="high" ${filters.priority === 'high' ? 'selected' : ''}>Kõrge</option>
                  <option value="critical" ${filters.priority === 'critical' ? 'selected' : ''}>Kriitiline</option>
                </select>
              </div>
              <div>
                <label for="machine_id" class="block text-sm font-medium text-gray-700 mb-2">Masin</label>
                <select name="machine_id" id="machine_id" class="w-full border-gray-300 rounded-md shadow-sm">
                  <option value="">Kõik masinad</option>
                  ${machineOptions}
                </select>
              </div>
              <div class="flex items-end">
                <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                  Filtreeri
                </button>
              </div>
            </form>
          </div>

          <!-- Issues Table -->
          <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-semibold">Rikked (${issues.length})</h2>
            </div>
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Masin</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pealkiri</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Operaator</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prioriteet</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Staatus</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kuupäev</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toimingud</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  ${issuesTableRows || '<tr><td colspan="8" class="px-6 py-4 text-center text-gray-500">Rikked puuduvad</td></tr>'}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <script>
          async function updateIssueStatus(issueId, currentStatus) {
            const statuses = ['open', 'in_progress', 'resolved', 'closed'];
            const statusLabels = {
              'open': 'Avatud',
              'in_progress': 'Töös',
              'resolved': 'Lahendatud',
              'closed': 'Suletud'
            };

            const newStatus = prompt(
              'Vali uus staatus:\\n' +
              statuses.map((s, i) => \`\${i + 1}. \${statusLabels[s]}\`).join('\\n'),
              statuses.indexOf(currentStatus) + 1
            );

            if (newStatus && newStatus >= 1 && newStatus <= 4) {
              const selectedStatus = statuses[newStatus - 1];

              try {
                const response = await fetch(\`/api/issues/\${issueId}\`, {
                  method: 'PUT',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ status: selectedStatus })
                });

                if (response.ok) {
                  alert('Staatus edukalt uuendatud!');
                  window.location.reload();
                } else {
                  alert('Viga staatuse uuendamisel!');
                }
              } catch (error) {
                alert('Viga: ' + error.message);
              }
            }
          }
        </script>
      </body>
      </html>
    `;

    return new Response(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });
  } catch (error) {
    console.error('Error loading issues management:', error);
    return c.text('Error loading issues management', 500);
  }
});

// Maintenance management page for administrators
pageRoutes.get('/admin/maintenance', async (c) => {
  try {
    const query = c.req.query();
    const filters = {};

    // Parse query parameters for filtering
    if (query.status) filters.status = query.status;
    if (query.urgency) filters.urgency = query.urgency;
    if (query.maintenance_type) filters.maintenance_type = query.maintenance_type;
    if (query.machine_id) filters.machine_id = parseInt(query.machine_id);

    const maintenanceRequests = await MaintenanceRequest.findAll(filters);
    const machines = await Machine.findAll();
    const maintenanceStats = await MaintenanceRequest.getStatistics();

    const maintenanceTableRows = maintenanceRequests.map(request => `
      <tr class="hover:bg-gray-50">
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
          #${request.id}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          ${request.machine_number}
        </td>
        <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
          ${request.title}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${request.operator_number}
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            request.maintenance_type === 'emergency' ? 'bg-red-100 text-red-800' :
            request.maintenance_type === 'preventive' ? 'bg-green-100 text-green-800' :
            request.maintenance_type === 'corrective' ? 'bg-yellow-100 text-yellow-800' :
            'bg-blue-100 text-blue-800'
          }">
            ${request.maintenance_type}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            request.urgency === 'urgent' ? 'bg-red-100 text-red-800' :
            request.urgency === 'high' ? 'bg-orange-100 text-orange-800' :
            request.urgency === 'medium' ? 'bg-yellow-100 text-yellow-800' :
            'bg-green-100 text-green-800'
          }">
            ${request.urgency}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            request.status === 'requested' ? 'bg-yellow-100 text-yellow-800' :
            request.status === 'scheduled' ? 'bg-blue-100 text-blue-800' :
            request.status === 'in_progress' ? 'bg-orange-100 text-orange-800' :
            request.status === 'completed' ? 'bg-green-100 text-green-800' :
            'bg-gray-100 text-gray-800'
          }">
            ${request.status}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${request.requested_date ? new Date(request.requested_date).toLocaleDateString('et-EE') :
            new Date(request.created_at).toLocaleDateString('et-EE')}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <a href="/admin/maintenance/${request.id}" class="text-blue-600 hover:text-blue-900 mr-2">
            Vaata
          </a>
          <button onclick="updateMaintenanceStatus(${request.id}, '${request.status}')"
                  class="text-green-600 hover:text-green-900">
            Muuda
          </button>
        </td>
      </tr>
    `).join('');

    const machineOptions = machines.map(machine => `
      <option value="${machine.id}" ${filters.machine_id === machine.id ? 'selected' : ''}>
        ${machine.machine_number} - ${machine.name}
      </option>
    `).join('');

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Hoolduse haldamine</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          <!-- Header -->
          <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">🔧 Hoolduse haldamine</h1>
            <a href="/admin" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">
              ← Tagasi töölauale
            </a>
          </div>

          <!-- Statistics -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">🔧</div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Kokku taotlused</p>
                  <p class="text-2xl font-semibold text-gray-900">${maintenanceStats.total_requests || 0}</p>
                </div>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">⏳</div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Ootel</p>
                  <p class="text-2xl font-semibold text-gray-900">${maintenanceStats.pending_requests || 0}</p>
                </div>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">📅</div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Planeeritud</p>
                  <p class="text-2xl font-semibold text-gray-900">${maintenanceStats.scheduled_requests || 0}</p>
                </div>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">🚨</div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Kiireloomulised</p>
                  <p class="text-2xl font-semibold text-gray-900">${maintenanceStats.urgent_requests || 0}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Filters -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold mb-4">Filtrid</h2>
            <form method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Staatus</label>
                <select name="status" id="status" class="w-full border-gray-300 rounded-md shadow-sm">
                  <option value="">Kõik staatused</option>
                  <option value="requested" ${filters.status === 'requested' ? 'selected' : ''}>Taotletud</option>
                  <option value="scheduled" ${filters.status === 'scheduled' ? 'selected' : ''}>Planeeritud</option>
                  <option value="in_progress" ${filters.status === 'in_progress' ? 'selected' : ''}>Töös</option>
                  <option value="completed" ${filters.status === 'completed' ? 'selected' : ''}>Lõpetatud</option>
                  <option value="cancelled" ${filters.status === 'cancelled' ? 'selected' : ''}>Tühistatud</option>
                </select>
              </div>
              <div>
                <label for="urgency" class="block text-sm font-medium text-gray-700 mb-2">Kiireloomulisus</label>
                <select name="urgency" id="urgency" class="w-full border-gray-300 rounded-md shadow-sm">
                  <option value="">Kõik tasemed</option>
                  <option value="low" ${filters.urgency === 'low' ? 'selected' : ''}>Madal</option>
                  <option value="medium" ${filters.urgency === 'medium' ? 'selected' : ''}>Keskmine</option>
                  <option value="high" ${filters.urgency === 'high' ? 'selected' : ''}>Kõrge</option>
                  <option value="urgent" ${filters.urgency === 'urgent' ? 'selected' : ''}>Kiire</option>
                </select>
              </div>
              <div>
                <label for="maintenance_type" class="block text-sm font-medium text-gray-700 mb-2">Tüüp</label>
                <select name="maintenance_type" id="maintenance_type" class="w-full border-gray-300 rounded-md shadow-sm">
                  <option value="">Kõik tüübid</option>
                  <option value="preventive" ${filters.maintenance_type === 'preventive' ? 'selected' : ''}>Ennetav</option>
                  <option value="corrective" ${filters.maintenance_type === 'corrective' ? 'selected' : ''}>Parandav</option>
                  <option value="emergency" ${filters.maintenance_type === 'emergency' ? 'selected' : ''}>Hädahooldus</option>
                  <option value="inspection" ${filters.maintenance_type === 'inspection' ? 'selected' : ''}>Kontroll</option>
                  <option value="calibration" ${filters.maintenance_type === 'calibration' ? 'selected' : ''}>Kalibreerimine</option>
                  <option value="cleaning" ${filters.maintenance_type === 'cleaning' ? 'selected' : ''}>Puhastamine</option>
                  <option value="other" ${filters.maintenance_type === 'other' ? 'selected' : ''}>Muu</option>
                </select>
              </div>
              <div>
                <label for="machine_id" class="block text-sm font-medium text-gray-700 mb-2">Masin</label>
                <select name="machine_id" id="machine_id" class="w-full border-gray-300 rounded-md shadow-sm">
                  <option value="">Kõik masinad</option>
                  ${machineOptions}
                </select>
              </div>
              <div class="flex items-end">
                <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                  Filtreeri
                </button>
              </div>
            </form>
          </div>

          <!-- Maintenance Table -->
          <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-semibold">Hoolduse taotlused (${maintenanceRequests.length})</h2>
            </div>
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Masin</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pealkiri</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Operaator</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tüüp</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kiireloomulisus</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Staatus</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kuupäev</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toimingud</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  ${maintenanceTableRows || '<tr><td colspan="9" class="px-6 py-4 text-center text-gray-500">Hoolduse taotlused puuduvad</td></tr>'}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <script>
          async function updateMaintenanceStatus(requestId, currentStatus) {
            const statuses = ['requested', 'scheduled', 'in_progress', 'completed', 'cancelled'];
            const statusLabels = {
              'requested': 'Taotletud',
              'scheduled': 'Planeeritud',
              'in_progress': 'Töös',
              'completed': 'Lõpetatud',
              'cancelled': 'Tühistatud'
            };

            const newStatus = prompt(
              'Vali uus staatus:\\n' +
              statuses.map((s, i) => \`\${i + 1}. \${statusLabels[s]}\`).join('\\n'),
              statuses.indexOf(currentStatus) + 1
            );

            if (newStatus && newStatus >= 1 && newStatus <= 5) {
              const selectedStatus = statuses[newStatus - 1];

              try {
                const response = await fetch(\`/api/maintenance/\${requestId}\`, {
                  method: 'PUT',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ status: selectedStatus })
                });

                if (response.ok) {
                  alert('Staatus edukalt uuendatud!');
                  window.location.reload();
                } else {
                  alert('Viga staatuse uuendamisel!');
                }
              } catch (error) {
                alert('Viga: ' + error.message);
              }
            }
          }
        </script>
      </body>
      </html>
    `;

    return new Response(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });
  } catch (error) {
    console.error('Error loading maintenance management:', error);
    return c.text('Error loading maintenance management', 500);
  }
});

// Issue detail view for administrators
pageRoutes.get('/admin/issues/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const issue = await Issue.findById(id);

    if (!issue) {
      return c.html(`
        <!DOCTYPE html>
        <html lang="et">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>CMMS - Rike ei leitud</title>
          <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100 min-h-screen flex items-center justify-center">
          <div class="max-w-md w-full mx-4">
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
              <div class="alert-error bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                Rike ei leitud
              </div>
              <div class="text-gray-600 mb-4">
                Rike ID "${id}" ei ole süsteemis registreeritud.
              </div>
              <a href="/admin/issues" class="inline-block bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
                Tagasi rikete nimekirja
              </a>
            </div>
          </div>
        </body>
        </html>
      `, 404);
    }

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Rike #${issue.id}</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          <!-- Header -->
          <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">🚨 Rike #${issue.id}</h1>
            <div class="space-x-2">
              <a href="/admin/issues" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">
                ← Tagasi nimekirja
              </a>
              <button onclick="updateIssueStatus(${issue.id}, '${issue.status}')"
                      class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                Muuda staatust
              </button>
            </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Issue Details -->
            <div class="lg:col-span-2 space-y-6">
              <!-- Issue Information -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Rike andmed</h2>
                <div class="space-y-4">
                  <div>
                    <h3 class="text-lg font-medium text-gray-900">${issue.title}</h3>
                    <div class="flex items-center space-x-4 mt-2">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        issue.priority === 'critical' ? 'bg-red-100 text-red-800' :
                        issue.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                        issue.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }">
                        ${issue.priority} prioriteet
                      </span>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        issue.status === 'open' ? 'bg-red-100 text-red-800' :
                        issue.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' :
                        issue.status === 'resolved' ? 'bg-green-100 text-green-800' :
                        'bg-gray-100 text-gray-800'
                      }">
                        ${issue.status}
                      </span>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                        ${issue.issue_type}
                      </span>
                    </div>
                  </div>

                  ${issue.description ? `
                    <div>
                      <h4 class="text-sm font-medium text-gray-700 mb-2">Kirjeldus</h4>
                      <p class="text-gray-900 whitespace-pre-wrap">${issue.description}</p>
                    </div>
                  ` : ''}

                  ${issue.resolution_notes ? `
                    <div>
                      <h4 class="text-sm font-medium text-gray-700 mb-2">Lahenduse märkused</h4>
                      <p class="text-gray-900 whitespace-pre-wrap">${issue.resolution_notes}</p>
                    </div>
                  ` : ''}
                </div>
              </div>

              <!-- Resolution Form -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Lahenduse märkused</h2>
                <form id="resolutionForm" class="space-y-4">
                  <div>
                    <label for="resolution_notes" class="block text-sm font-medium text-gray-700 mb-2">
                      Lahenduse kirjeldus
                    </label>
                    <textarea name="resolution_notes"
                              id="resolution_notes"
                              rows="4"
                              placeholder="Kirjelda, kuidas rike lahendati..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">${issue.resolution_notes || ''}</textarea>
                  </div>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label for="assigned_to" class="block text-sm font-medium text-gray-700 mb-2">
                        Määratud isik
                      </label>
                      <input type="text"
                             name="assigned_to"
                             id="assigned_to"
                             value="${issue.assigned_to || ''}"
                             placeholder="Nimi või meeskond"
                             class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                    </div>
                    <div>
                      <label for="partner_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Väline partner
                      </label>
                      <select name="partner_id" id="partner_id"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                        <option value="">Vali partner...</option>
                      </select>
                    </div>
                  </div>
                  <div>
                    <label for="partner_notes" class="block text-sm font-medium text-gray-700 mb-2">
                      Märkused partnerile
                    </label>
                    <textarea name="partner_notes"
                              id="partner_notes"
                              rows="3"
                              placeholder="Erilised juhised või märkused partnerile..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">${issue.partner_notes || ''}</textarea>
                  </div>
                  <button type="submit" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
                    Salvesta märkused
                  </button>
                </form>
              </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
              <!-- Machine Information -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Masin</h2>
                <dl class="space-y-2">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Number</dt>
                    <dd class="text-sm text-gray-900">${issue.machine_number}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Nimi</dt>
                    <dd class="text-sm text-gray-900">${issue.machine_name}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Asukoht</dt>
                    <dd class="text-sm text-gray-900">${issue.machine_location || 'Määramata'}</dd>
                  </div>
                </dl>
                <a href="/machines/${issue.machine_id}" class="mt-4 inline-block bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm">
                  Vaata masinat
                </a>
              </div>

              <!-- Operator Information -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Operaator</h2>
                <dl class="space-y-2">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Number</dt>
                    <dd class="text-sm text-gray-900">${issue.operator_number}</dd>
                  </div>
                  ${issue.operator_name ? `
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Nimi</dt>
                      <dd class="text-sm text-gray-900">${issue.operator_name}</dd>
                    </div>
                  ` : ''}
                  ${issue.assigned_to ? `
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Määratud</dt>
                      <dd class="text-sm text-gray-900">${issue.assigned_to}</dd>
                    </div>
                  ` : ''}
                </dl>
              </div>

              ${issue.partner_company ? `
                <!-- Partner Information -->
                <div class="bg-white rounded-lg shadow-md p-6">
                  <h2 class="text-xl font-semibold mb-4">🤝 Väline partner</h2>
                  <dl class="space-y-2">
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Ettevõte</dt>
                      <dd class="text-sm text-gray-900 font-medium">${issue.partner_company}</dd>
                    </div>
                    ${issue.partner_contact ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">Kontaktisik</dt>
                        <dd class="text-sm text-gray-900">${issue.partner_contact}</dd>
                      </div>
                    ` : ''}
                    ${issue.partner_email ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">E-post</dt>
                        <dd class="text-sm text-gray-900">
                          <a href="mailto:${issue.partner_email}" class="text-blue-600 hover:text-blue-800">
                            ${issue.partner_email}
                          </a>
                        </dd>
                      </div>
                    ` : ''}
                    ${issue.partner_phone ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">Telefon</dt>
                        <dd class="text-sm text-gray-900">
                          <a href="tel:${issue.partner_phone}" class="text-blue-600 hover:text-blue-800">
                            ${issue.partner_phone}
                          </a>
                        </dd>
                      </div>
                    ` : ''}
                    ${issue.partner_notes ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">Märkused</dt>
                        <dd class="text-sm text-gray-900 bg-yellow-50 border border-yellow-200 rounded p-2">
                          ${issue.partner_notes}
                        </dd>
                      </div>
                    ` : ''}
                  </dl>
                </div>
              ` : ''}

              <!-- Timeline -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Ajalugu</h2>
                <dl class="space-y-2">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Teatatud</dt>
                    <dd class="text-sm text-gray-900">${new Date(issue.reported_at).toLocaleString('et-EE')}</dd>
                  </div>
                  ${issue.resolved_at ? `
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Lahendatud</dt>
                      <dd class="text-sm text-gray-900">${new Date(issue.resolved_at).toLocaleString('et-EE')}</dd>
                    </div>
                  ` : ''}
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Viimati uuendatud</dt>
                    <dd class="text-sm text-gray-900">${new Date(issue.updated_at).toLocaleString('et-EE')}</dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <script>
          // Load partners on page load
          document.addEventListener('DOMContentLoaded', function() {
            loadPartners();
          });

          async function loadPartners() {
            try {
              const response = await fetch('/api/partners?is_active=true');
              const partners = await response.json();

              const partnerSelect = document.getElementById('partner_id');
              partnerSelect.innerHTML = '<option value="">Vali partner...</option>';

              partners.forEach(partner => {
                const option = document.createElement('option');
                option.value = partner.id;
                option.textContent = \`\${partner.company_name} - \${partner.specializations.join(', ')}\`;

                // Set selected if this partner is already assigned
                if (partner.id == ${issue.partner_id || 'null'}) {
                  option.selected = true;
                }

                partnerSelect.appendChild(option);
              });
            } catch (error) {
              console.error('Error loading partners:', error);
            }
          }

          async function updateIssueStatus(issueId, currentStatus) {
            const statuses = ['open', 'in_progress', 'resolved', 'closed'];
            const statusLabels = {
              'open': 'Avatud',
              'in_progress': 'Töös',
              'resolved': 'Lahendatud',
              'closed': 'Suletud'
            };

            const newStatus = prompt(
              'Vali uus staatus:\\n' +
              statuses.map((s, i) => \`\${i + 1}. \${statusLabels[s]}\`).join('\\n'),
              statuses.indexOf(currentStatus) + 1
            );

            if (newStatus && newStatus >= 1 && newStatus <= 4) {
              const selectedStatus = statuses[newStatus - 1];

              try {
                const response = await fetch(\`/api/issues/\${issueId}\`, {
                  method: 'PUT',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ status: selectedStatus })
                });

                if (response.ok) {
                  alert('Staatus edukalt uuendatud!');
                  window.location.reload();
                } else {
                  alert('Viga staatuse uuendamisel!');
                }
              } catch (error) {
                alert('Viga: ' + error.message);
              }
            }
          }

          document.getElementById('resolutionForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const updateData = Object.fromEntries(formData.entries());

            try {
              const response = await fetch(\`/api/issues/${issue.id}\`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(updateData)
              });

              if (response.ok) {
                alert('Märkused edukalt salvestatud!');
                window.location.reload();
              } else {
                alert('Viga märkuste salvestamisel!');
              }
            } catch (error) {
              alert('Viga: ' + error.message);
            }
          });
        </script>
      </body>
      </html>
    `;

    return new Response(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });
  } catch (error) {
    console.error('Error loading issue detail:', error);
    return c.text('Error loading issue detail', 500);
  }
});

// Maintenance detail view for administrators
pageRoutes.get('/admin/maintenance/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const maintenanceRequest = await MaintenanceRequest.findById(id);

    if (!maintenanceRequest) {
      return c.html(`
        <!DOCTYPE html>
        <html lang="et">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>CMMS - Hoolduse taotlus ei leitud</title>
          <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100 min-h-screen flex items-center justify-center">
          <div class="max-w-md w-full mx-4">
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
              <div class="alert-error bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                Hoolduse taotlus ei leitud
              </div>
              <div class="text-gray-600 mb-4">
                Hoolduse taotlus ID "${id}" ei ole süsteemis registreeritud.
              </div>
              <a href="/admin/maintenance" class="inline-block bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded">
                Tagasi hoolduse nimekirja
              </a>
            </div>
          </div>
        </body>
        </html>
      `, 404);
    }

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Hoolduse taotlus #${maintenanceRequest.id}</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          <!-- Header -->
          <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">🔧 Hoolduse taotlus #${maintenanceRequest.id}</h1>
            <div class="space-x-2">
              <a href="/admin/maintenance" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">
                ← Tagasi nimekirja
              </a>
              <button onclick="updateMaintenanceStatus(${maintenanceRequest.id}, '${maintenanceRequest.status}')"
                      class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                Muuda staatust
              </button>
            </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Maintenance Details -->
            <div class="lg:col-span-2 space-y-6">
              <!-- Maintenance Information -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Hoolduse andmed</h2>
                <div class="space-y-4">
                  <div>
                    <h3 class="text-lg font-medium text-gray-900">${maintenanceRequest.title}</h3>
                    <div class="flex items-center space-x-4 mt-2">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        maintenanceRequest.maintenance_type === 'emergency' ? 'bg-red-100 text-red-800' :
                        maintenanceRequest.maintenance_type === 'preventive' ? 'bg-green-100 text-green-800' :
                        maintenanceRequest.maintenance_type === 'corrective' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-blue-100 text-blue-800'
                      }">
                        ${maintenanceRequest.maintenance_type}
                      </span>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        maintenanceRequest.urgency === 'urgent' ? 'bg-red-100 text-red-800' :
                        maintenanceRequest.urgency === 'high' ? 'bg-orange-100 text-orange-800' :
                        maintenanceRequest.urgency === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }">
                        ${maintenanceRequest.urgency}
                      </span>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        maintenanceRequest.status === 'requested' ? 'bg-yellow-100 text-yellow-800' :
                        maintenanceRequest.status === 'scheduled' ? 'bg-blue-100 text-blue-800' :
                        maintenanceRequest.status === 'in_progress' ? 'bg-orange-100 text-orange-800' :
                        maintenanceRequest.status === 'completed' ? 'bg-green-100 text-green-800' :
                        'bg-gray-100 text-gray-800'
                      }">
                        ${maintenanceRequest.status}
                      </span>
                    </div>
                  </div>

                  ${maintenanceRequest.description ? `
                    <div>
                      <h4 class="text-sm font-medium text-gray-700 mb-2">Kirjeldus</h4>
                      <p class="text-gray-900 whitespace-pre-wrap">${maintenanceRequest.description}</p>
                    </div>
                  ` : ''}

                  ${maintenanceRequest.maintenance_notes ? `
                    <div>
                      <h4 class="text-sm font-medium text-gray-700 mb-2">Hoolduse märkused</h4>
                      <p class="text-gray-900 whitespace-pre-wrap">${maintenanceRequest.maintenance_notes}</p>
                    </div>
                  ` : ''}

                  ${maintenanceRequest.completion_notes ? `
                    <div>
                      <h4 class="text-sm font-medium text-gray-700 mb-2">Lõpetamise märkused</h4>
                      <p class="text-gray-900 whitespace-pre-wrap">${maintenanceRequest.completion_notes}</p>
                    </div>
                  ` : ''}
                </div>
              </div>

              <!-- Maintenance Management Form -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Hoolduse haldamine</h2>
                <form id="maintenanceForm" class="space-y-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label for="assigned_to" class="block text-sm font-medium text-gray-700 mb-2">
                        Määratud isik/meeskond
                      </label>
                      <input type="text"
                             name="assigned_to"
                             id="assigned_to"
                             value="${maintenanceRequest.assigned_to || ''}"
                             placeholder="Nimi või meeskond"
                             class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                    </div>
                    <div>
                      <label for="partner_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Väline partner
                      </label>
                      <select name="partner_id" id="partner_id"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                        <option value="">Vali partner...</option>
                      </select>
                    </div>
                    <div>
                      <label for="scheduled_date" class="block text-sm font-medium text-gray-700 mb-2">
                        Planeeritud kuupäev
                      </label>
                      <input type="date"
                             name="scheduled_date"
                             id="scheduled_date"
                             value="${maintenanceRequest.scheduled_date ? new Date(maintenanceRequest.scheduled_date).toISOString().split('T')[0] : ''}"
                             class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                    </div>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label for="estimated_duration" class="block text-sm font-medium text-gray-700 mb-2">
                        Hinnanguline kestus (minutites)
                      </label>
                      <input type="number"
                             name="estimated_duration"
                             id="estimated_duration"
                             value="${maintenanceRequest.estimated_duration || ''}"
                             placeholder="60"
                             class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                    </div>
                    <div>
                      <label for="cost" class="block text-sm font-medium text-gray-700 mb-2">
                        Maksumus (€)
                      </label>
                      <input type="number"
                             name="cost"
                             id="cost"
                             step="0.01"
                             value="${maintenanceRequest.cost || ''}"
                             placeholder="0.00"
                             class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                    </div>
                  </div>

                  <div>
                    <label for="partner_notes" class="block text-sm font-medium text-gray-700 mb-2">
                      Märkused partnerile
                    </label>
                    <textarea name="partner_notes"
                              id="partner_notes"
                              rows="2"
                              placeholder="Erilised juhised või märkused partnerile..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">${maintenanceRequest.partner_notes || ''}</textarea>
                  </div>

                  <div>
                    <label for="maintenance_notes" class="block text-sm font-medium text-gray-700 mb-2">
                      Hoolduse märkused
                    </label>
                    <textarea name="maintenance_notes"
                              id="maintenance_notes"
                              rows="3"
                              placeholder="Hoolduse käigus tehtud märkused..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">${maintenanceRequest.maintenance_notes || ''}</textarea>
                  </div>

                  <div>
                    <label for="completion_notes" class="block text-sm font-medium text-gray-700 mb-2">
                      Lõpetamise märkused
                    </label>
                    <textarea name="completion_notes"
                              id="completion_notes"
                              rows="3"
                              placeholder="Hoolduse lõpetamise märkused..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">${maintenanceRequest.completion_notes || ''}</textarea>
                  </div>

                  <button type="submit" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded">
                    Salvesta andmed
                  </button>
                </form>
              </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
              <!-- Machine Information -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Masin</h2>
                <dl class="space-y-2">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Number</dt>
                    <dd class="text-sm text-gray-900">${maintenanceRequest.machine_number}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Nimi</dt>
                    <dd class="text-sm text-gray-900">${maintenanceRequest.machine_name}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Asukoht</dt>
                    <dd class="text-sm text-gray-900">${maintenanceRequest.machine_location || 'Määramata'}</dd>
                  </div>
                </dl>
                <a href="/machines/${maintenanceRequest.machine_id}" class="mt-4 inline-block bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm">
                  Vaata masinat
                </a>
              </div>

              <!-- Operator Information -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Operaator</h2>
                <dl class="space-y-2">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Number</dt>
                    <dd class="text-sm text-gray-900">${maintenanceRequest.operator_number}</dd>
                  </div>
                  ${maintenanceRequest.operator_name ? `
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Nimi</dt>
                      <dd class="text-sm text-gray-900">${maintenanceRequest.operator_name}</dd>
                    </div>
                  ` : ''}
                </dl>
              </div>

              <!-- Partner Information -->
              ${maintenanceRequest.partner_company ? `
                <div class="bg-white rounded-lg shadow-md p-6">
                  <h2 class="text-xl font-semibold mb-4">Väline Partner</h2>
                  <dl class="space-y-2">
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Ettevõte</dt>
                      <dd class="text-sm text-gray-900">${maintenanceRequest.partner_company}</dd>
                    </div>
                    ${maintenanceRequest.partner_contact ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">Kontaktisik</dt>
                        <dd class="text-sm text-gray-900">${maintenanceRequest.partner_contact}</dd>
                      </div>
                    ` : ''}
                    ${maintenanceRequest.partner_email ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">E-post</dt>
                        <dd class="text-sm text-gray-900">
                          <a href="mailto:${maintenanceRequest.partner_email}" class="text-blue-600 hover:text-blue-800">
                            ${maintenanceRequest.partner_email}
                          </a>
                        </dd>
                      </div>
                    ` : ''}
                    ${maintenanceRequest.partner_phone ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">Telefon</dt>
                        <dd class="text-sm text-gray-900">
                          <a href="tel:${maintenanceRequest.partner_phone}" class="text-blue-600 hover:text-blue-800">
                            ${maintenanceRequest.partner_phone}
                          </a>
                        </dd>
                      </div>
                    ` : ''}
                    ${maintenanceRequest.partner_notes ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">Märkused partnerile</dt>
                        <dd class="text-sm text-gray-900">${maintenanceRequest.partner_notes}</dd>
                      </div>
                    ` : ''}
                  </dl>
                </div>
              ` : ''}

              <!-- Timeline -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Ajalugu</h2>
                <dl class="space-y-2">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Taotletud</dt>
                    <dd class="text-sm text-gray-900">${maintenanceRequest.requested_date ?
                      new Date(maintenanceRequest.requested_date).toLocaleDateString('et-EE') :
                      new Date(maintenanceRequest.created_at).toLocaleDateString('et-EE')}</dd>
                  </div>
                  ${maintenanceRequest.scheduled_date ? `
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Planeeritud</dt>
                      <dd class="text-sm text-gray-900">${new Date(maintenanceRequest.scheduled_date).toLocaleDateString('et-EE')}</dd>
                    </div>
                  ` : ''}
                  ${maintenanceRequest.completed_date ? `
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Lõpetatud</dt>
                      <dd class="text-sm text-gray-900">${new Date(maintenanceRequest.completed_date).toLocaleDateString('et-EE')}</dd>
                    </div>
                  ` : ''}
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Viimati uuendatud</dt>
                    <dd class="text-sm text-gray-900">${new Date(maintenanceRequest.updated_at).toLocaleString('et-EE')}</dd>
                  </div>
                </dl>
              </div>

              <!-- Duration & Cost -->
              ${maintenanceRequest.estimated_duration || maintenanceRequest.actual_duration || maintenanceRequest.cost ? `
                <div class="bg-white rounded-lg shadow-md p-6">
                  <h2 class="text-xl font-semibold mb-4">Kestus & Maksumus</h2>
                  <dl class="space-y-2">
                    ${maintenanceRequest.estimated_duration ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">Hinnanguline kestus</dt>
                        <dd class="text-sm text-gray-900">${maintenanceRequest.estimated_duration} min</dd>
                      </div>
                    ` : ''}
                    ${maintenanceRequest.actual_duration ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">Tegelik kestus</dt>
                        <dd class="text-sm text-gray-900">${maintenanceRequest.actual_duration} min</dd>
                      </div>
                    ` : ''}
                    ${maintenanceRequest.cost ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">Maksumus</dt>
                        <dd class="text-sm text-gray-900">${maintenanceRequest.cost} €</dd>
                      </div>
                    ` : ''}
                  </dl>
                </div>
              ` : ''}
            </div>
          </div>
        </div>

        <script>
          async function updateMaintenanceStatus(requestId, currentStatus) {
            const statuses = ['requested', 'scheduled', 'in_progress', 'completed', 'cancelled'];
            const statusLabels = {
              'requested': 'Taotletud',
              'scheduled': 'Planeeritud',
              'in_progress': 'Töös',
              'completed': 'Lõpetatud',
              'cancelled': 'Tühistatud'
            };

            const newStatus = prompt(
              'Vali uus staatus:\\n' +
              statuses.map((s, i) => \`\${i + 1}. \${statusLabels[s]}\`).join('\\n'),
              statuses.indexOf(currentStatus) + 1
            );

            if (newStatus && newStatus >= 1 && newStatus <= 5) {
              const selectedStatus = statuses[newStatus - 1];

              try {
                const response = await fetch(\`/api/maintenance/\${requestId}\`, {
                  method: 'PUT',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ status: selectedStatus })
                });

                if (response.ok) {
                  alert('Staatus edukalt uuendatud!');
                  window.location.reload();
                } else {
                  alert('Viga staatuse uuendamisel!');
                }
              } catch (error) {
                alert('Viga: ' + error.message);
              }
            }
          }

          // Load partners on page load
          document.addEventListener('DOMContentLoaded', function() {
            loadPartners();
          });

          async function loadPartners() {
            try {
              const response = await fetch('/api/partners?is_active=true');
              const partners = await response.json();

              const partnerSelect = document.getElementById('partner_id');
              partnerSelect.innerHTML = '<option value="">Vali partner...</option>';

              partners.forEach(partner => {
                const option = document.createElement('option');
                option.value = partner.id;
                option.textContent = \`\${partner.company_name} - \${partner.specializations.join(', ')}\`;

                // Set selected if this partner is already assigned
                if (partner.id == ${maintenanceRequest.partner_id || 'null'}) {
                  option.selected = true;
                }

                partnerSelect.appendChild(option);
              });
            } catch (error) {
              console.error('Error loading partners:', error);
            }
          }

          document.getElementById('maintenanceForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const updateData = Object.fromEntries(formData.entries());

            // Convert empty strings to null for optional fields
            Object.keys(updateData).forEach(key => {
              if (updateData[key] === '') {
                updateData[key] = null;
              }
            });

            try {
              const response = await fetch(\`/api/maintenance/${maintenanceRequest.id}\`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(updateData)
              });

              if (response.ok) {
                alert('Andmed edukalt salvestatud!');
                window.location.reload();
              } else {
                alert('Viga andmete salvestamisel!');
              }
            } catch (error) {
              alert('Viga: ' + error.message);
            }
          });
        </script>
      </body>
      </html>
    `;

    return new Response(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });
  } catch (error) {
    console.error('Error loading maintenance detail:', error);
    return c.text('Error loading maintenance detail', 500);
  }
});

// Admin partners page
pageRoutes.get('/admin/partners', async (c) => {
  try {
    const html = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Hoolduspartnerid - CMMS</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
      </head>
      <body class="bg-gray-50">
        <div class="min-h-screen">
          <!-- Navigation -->
          <nav class="bg-blue-600 text-white p-4">
            <div class="container mx-auto flex justify-between items-center">
              <div class="flex items-center space-x-4">
                <h1 class="text-xl font-bold">CMMS - Hoolduspartnerid</h1>
              </div>
              <div class="flex space-x-4">
                <a href="/admin" class="hover:bg-blue-700 px-3 py-2 rounded">
                  <i class="fas fa-home mr-2"></i>Dashboard
                </a>
                <a href="/admin/machines" class="hover:bg-blue-700 px-3 py-2 rounded">
                  <i class="fas fa-cogs mr-2"></i>Masinad
                </a>
                <a href="/admin/issues" class="hover:bg-blue-700 px-3 py-2 rounded">
                  <i class="fas fa-exclamation-triangle mr-2"></i>Rikked
                </a>
                <a href="/admin/maintenance" class="hover:bg-blue-700 px-3 py-2 rounded">
                  <i class="fas fa-wrench mr-2"></i>Hooldus
                </a>
                <a href="/admin/partners" class="bg-blue-800 px-3 py-2 rounded">
                  <i class="fas fa-handshake mr-2"></i>Partnerid
                </a>
              </div>
            </div>
          </nav>

          <div class="container mx-auto px-4 py-8">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-2xl font-bold text-gray-800">Hoolduspartnerid</h2>
              <button onclick="openAddPartnerModal()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                <i class="fas fa-plus mr-2"></i>Lisa Partner
              </button>
            </div>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-handshake text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Kokku Partnereid</p>
                    <p class="text-2xl font-semibold text-gray-900" id="total-partners">-</p>
                  </div>
                </div>
              </div>

              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-check-circle text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Aktiivsed</p>
                    <p class="text-2xl font-semibold text-gray-900" id="active-partners">-</p>
                  </div>
                </div>
              </div>

              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                    <i class="fas fa-tasks text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Aktiivsed Taotlused</p>
                    <p class="text-2xl font-semibold text-gray-900" id="active-requests">-</p>
                  </div>
                </div>
              </div>

              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <i class="fas fa-euro-sign text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Keskmine Tunnitasu</p>
                    <p class="text-2xl font-semibold text-gray-900" id="avg-rate">-</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Partners Table -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ettevõte</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kontakt</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spetsialiseerumised</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tunnitasu</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aktiivsed Taotlused</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Staatus</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toimingud</th>
                    </tr>
                  </thead>
                  <tbody id="partners-table-body" class="bg-white divide-y divide-gray-200">
                    <!-- Partners will be loaded here -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <script>
          let partners = [];

          // Load data on page load
          document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadPartners();
          });

          async function loadStatistics() {
            try {
              const response = await fetch('/api/partners/statistics');
              const stats = await response.json();

              document.getElementById('total-partners').textContent = stats.total_partners || 0;
              document.getElementById('active-partners').textContent = stats.active_partners || 0;
              document.getElementById('active-requests').textContent = stats.total_partner_requests || 0;
              document.getElementById('avg-rate').textContent = stats.average_hourly_rate ?
                parseFloat(stats.average_hourly_rate).toFixed(2) + '€' : '-';
            } catch (error) {
              console.error('Error loading statistics:', error);
            }
          }

          async function loadPartners() {
            try {
              const response = await fetch('/api/partners');
              partners = await response.json();

              renderPartnersTable();
            } catch (error) {
              console.error('Error loading partners:', error);
            }
          }

          function renderPartnersTable() {
            const tbody = document.getElementById('partners-table-body');
            tbody.innerHTML = '';

            partners.forEach(partner => {
              const row = document.createElement('tr');
              row.className = 'hover:bg-gray-50';

              const statusBadge = partner.is_active ?
                '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Aktiivne</span>' :
                '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Mitteaktiivne</span>';

              const specialsBadges = partner.specializations.map(spec =>
                \`<span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">\${spec}</span>\`
              ).join(' ');

              row.innerHTML = \`
                <td class="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div class="text-sm font-medium text-gray-900">\${partner.company_name}</div>
                    <div class="text-sm text-gray-500">\${partner.email}</div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">\${partner.contact_person || '-'}</div>
                  <div class="text-sm text-gray-500">\${partner.phone || '-'}</div>
                </td>
                <td class="px-6 py-4">
                  <div class="flex flex-wrap gap-1">\${specialsBadges}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  \${partner.hourly_rate ? parseFloat(partner.hourly_rate).toFixed(2) + '€' : '-'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  \${partner.active_requests}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  \${statusBadge}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button onclick="viewPartnerRequests(\${partner.id})" class="text-green-600 hover:text-green-900 mr-3" title="Vaata taotlusi">
                    <i class="fas fa-tasks"></i>
                  </button>
                  <button onclick="editPartner(\${partner.id})" class="text-blue-600 hover:text-blue-900 mr-3" title="Muuda">
                    <i class="fas fa-edit"></i>
                  </button>
                  \${partner.is_active ?
                    \`<button onclick="togglePartnerStatus(\${partner.id}, false)" class="text-orange-600 hover:text-orange-900 mr-3" title="Deaktiveeri">
                      <i class="fas fa-pause"></i>
                    </button>\` :
                    \`<button onclick="togglePartnerStatus(\${partner.id}, true)" class="text-green-600 hover:text-green-900 mr-3" title="Aktiveeri">
                      <i class="fas fa-play"></i>
                    </button>\`
                  }
                  <button onclick="deletePartner(\${partner.id})" class="text-red-600 hover:text-red-900" title="Kustuta">
                    <i class="fas fa-trash"></i>
                  </button>
                </td>
              \`;

              tbody.appendChild(row);
            });
          }

          function openAddPartnerModal() {
            alert('Partner modal tuleb järgmises versioonis!');
          }

          function editPartner(id) {
            alert(\`Partneri \${id} muutmine tuleb järgmises versioonis!\`);
          }

          async function togglePartnerStatus(id, activate) {
            try {
              const action = activate ? 'activate' : 'deactivate';
              const response = await fetch(\`/api/partners/\${id}/\${action}\`, {
                method: 'POST'
              });

              if (response.ok) {
                loadPartners();
                loadStatistics();
                alert(\`Partner \${activate ? 'aktiveeritud' : 'deaktiveeritud'}!\`);
              } else {
                const error = await response.json();
                alert('Viga: ' + (error.error || 'Teadmata viga'));
              }
            } catch (error) {
              console.error('Error toggling partner status:', error);
              alert('Viga partneri staatuse muutmisel');
            }
          }

          async function deletePartner(id) {
            if (!confirm('Kas olete kindel, et soovite selle partneri kustutada?')) {
              return;
            }

            try {
              const response = await fetch(\`/api/partners/\${id}\`, {
                method: 'DELETE'
              });

              if (response.ok) {
                loadPartners();
                loadStatistics();
                alert('Partner edukalt kustutatud!');
              } else {
                const error = await response.json();
                alert('Viga: ' + (error.error || 'Teadmata viga'));
              }
            } catch (error) {
              console.error('Error deleting partner:', error);
              alert('Viga partneri kustutamisel');
            }
          }

          function viewPartnerRequests(id) {
            window.open(\`/admin/partners/\${id}/requests\`, '_blank');
          }
        </script>
      </body>
      </html>
    `;

    return c.html(html);
  } catch (error) {
    console.error('Error loading partners page:', error);
    return c.text('Error loading page', 500);
  }
});

// Admin partner requests page
pageRoutes.get('/admin/partners/:id/requests', async (c) => {
  try {
    const partnerId = parseInt(c.req.param('id'));

    const html = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Partneri Taotlused - CMMS</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
      </head>
      <body class="bg-gray-50">
        <div class="min-h-screen">
          <!-- Navigation -->
          <nav class="bg-blue-600 text-white p-4">
            <div class="container mx-auto flex justify-between items-center">
              <div class="flex items-center space-x-4">
                <h1 class="text-xl font-bold">CMMS - Partneri Taotlused</h1>
              </div>
              <div class="flex space-x-4">
                <a href="/admin" class="hover:bg-blue-700 px-3 py-2 rounded">
                  <i class="fas fa-home mr-2"></i>Dashboard
                </a>
                <a href="/admin/partners" class="hover:bg-blue-700 px-3 py-2 rounded">
                  <i class="fas fa-handshake mr-2"></i>Tagasi Partneritele
                </a>
              </div>
            </div>
          </nav>

          <div class="container mx-auto px-4 py-8">
            <!-- Partner Info Header -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
              <div class="flex items-center justify-between">
                <div>
                  <h2 class="text-2xl font-bold text-gray-800" id="partner-name">Laadin...</h2>
                  <p class="text-gray-600" id="partner-email">-</p>
                </div>
                <div class="text-right">
                  <p class="text-sm text-gray-500">Kontakt</p>
                  <p class="text-lg font-semibold" id="partner-contact">-</p>
                  <p class="text-gray-600" id="partner-phone">-</p>
                </div>
              </div>
              <div class="mt-4">
                <p class="text-sm text-gray-500">Spetsialiseerumised</p>
                <div id="partner-specializations" class="flex flex-wrap gap-2 mt-1">
                  <!-- Specializations will be loaded here -->
                </div>
              </div>
            </div>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-tasks text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Kokku Taotlusi</p>
                    <p class="text-2xl font-semibold text-gray-900" id="total-requests">-</p>
                  </div>
                </div>
              </div>

              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                    <i class="fas fa-clock text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Ootel</p>
                    <p class="text-2xl font-semibold text-gray-900" id="pending-requests">-</p>
                  </div>
                </div>
              </div>

              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-calendar text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Planeeritud</p>
                    <p class="text-2xl font-semibold text-gray-900" id="scheduled-requests">-</p>
                  </div>
                </div>
              </div>

              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-check-circle text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Lõpetatud</p>
                    <p class="text-2xl font-semibold text-gray-900" id="completed-requests">-</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Filters -->
            <div class="bg-white p-4 rounded-lg shadow mb-6">
              <div class="flex flex-wrap gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Staatus</label>
                  <select id="status-filter" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Kõik</option>
                    <option value="requested">Taotletud</option>
                    <option value="scheduled">Planeeritud</option>
                    <option value="in_progress">Töös</option>
                    <option value="completed">Lõpetatud</option>
                    <option value="cancelled">Tühistatud</option>
                  </select>
                </div>
                <div class="flex items-end">
                  <button onclick="loadRequests()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    <i class="fas fa-search mr-2"></i>Otsi
                  </button>
                </div>
              </div>
            </div>

            <!-- Requests Table -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Taotlus</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Masin</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tüüp</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kiireloomulisus</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kuupäevad</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Staatus</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toimingud</th>
                    </tr>
                  </thead>
                  <tbody id="requests-table-body" class="bg-white divide-y divide-gray-200">
                    <!-- Requests will be loaded here -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <script>
          const partnerId = ${partnerId};
          let partner = null;
          let requests = [];

          // Load data on page load
          document.addEventListener('DOMContentLoaded', function() {
            loadPartner();
            loadRequests();
          });

          async function loadPartner() {
            try {
              const response = await fetch(\`/api/partners/\${partnerId}\`);
              partner = await response.json();

              document.getElementById('partner-name').textContent = partner.company_name;
              document.getElementById('partner-email').textContent = partner.email;
              document.getElementById('partner-contact').textContent = partner.contact_person || '-';
              document.getElementById('partner-phone').textContent = partner.phone || '-';

              // Update specializations
              const specializationsContainer = document.getElementById('partner-specializations');
              specializationsContainer.innerHTML = '';
              partner.specializations.forEach(spec => {
                const badge = document.createElement('span');
                badge.className = 'px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded';
                badge.textContent = spec;
                specializationsContainer.appendChild(badge);
              });
            } catch (error) {
              console.error('Error loading partner:', error);
            }
          }

          async function loadRequests() {
            try {
              const status = document.getElementById('status-filter').value;

              let url = \`/api/partners/\${partnerId}/requests\`;
              if (status) {
                url += \`?status=\${status}\`;
              }

              const response = await fetch(url);
              requests = await response.json();

              updateStatistics();
              renderRequestsTable();
            } catch (error) {
              console.error('Error loading requests:', error);
            }
          }

          function updateStatistics() {
            const stats = {
              total: requests.length,
              requested: requests.filter(r => r.status === 'requested').length,
              scheduled: requests.filter(r => r.status === 'scheduled').length,
              in_progress: requests.filter(r => r.status === 'in_progress').length,
              completed: requests.filter(r => r.status === 'completed').length
            };

            document.getElementById('total-requests').textContent = stats.total;
            document.getElementById('pending-requests').textContent = stats.requested;
            document.getElementById('scheduled-requests').textContent = stats.scheduled;
            document.getElementById('completed-requests').textContent = stats.completed;
          }

          function renderRequestsTable() {
            const tbody = document.getElementById('requests-table-body');
            tbody.innerHTML = '';

            if (requests.length === 0) {
              const row = document.createElement('tr');
              row.innerHTML = \`
                <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                  Sellel partneril pole veel taotlusi
                </td>
              \`;
              tbody.appendChild(row);
              return;
            }

            requests.forEach(request => {
              const row = document.createElement('tr');
              row.className = 'hover:bg-gray-50';

              const statusColors = {
                'requested': 'bg-yellow-100 text-yellow-800',
                'scheduled': 'bg-blue-100 text-blue-800',
                'in_progress': 'bg-orange-100 text-orange-800',
                'completed': 'bg-green-100 text-green-800',
                'cancelled': 'bg-red-100 text-red-800'
              };

              const urgencyColors = {
                'low': 'bg-green-100 text-green-800',
                'medium': 'bg-yellow-100 text-yellow-800',
                'high': 'bg-orange-100 text-orange-800',
                'urgent': 'bg-red-100 text-red-800'
              };

              const typeColors = {
                'preventive': 'bg-green-100 text-green-800',
                'corrective': 'bg-yellow-100 text-yellow-800',
                'emergency': 'bg-red-100 text-red-800',
                'inspection': 'bg-blue-100 text-blue-800',
                'calibration': 'bg-purple-100 text-purple-800',
                'cleaning': 'bg-cyan-100 text-cyan-800',
                'other': 'bg-gray-100 text-gray-800'
              };

              row.innerHTML = \`
                <td class="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div class="text-sm font-medium text-gray-900">\${request.title}</div>
                    <div class="text-sm text-gray-500">ID: \${request.id}</div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div class="text-sm font-medium text-gray-900">\${request.machine_number}</div>
                    <div class="text-sm text-gray-500">\${request.machine_name}</div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs font-semibold rounded-full \${typeColors[request.maintenance_type] || 'bg-gray-100 text-gray-800'}">
                    \${request.maintenance_type}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs font-semibold rounded-full \${urgencyColors[request.urgency] || 'bg-gray-100 text-gray-800'}">
                    \${request.urgency}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>Taotletud: \${new Date(request.created_at).toLocaleDateString('et-EE')}</div>
                  \${request.scheduled_date ? \`<div>Planeeritud: \${new Date(request.scheduled_date).toLocaleDateString('et-EE')}</div>\` : ''}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs font-semibold rounded-full \${statusColors[request.status] || 'bg-gray-100 text-gray-800'}">
                    \${request.status}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <a href="/admin/maintenance/\${request.id}" class="text-blue-600 hover:text-blue-900" title="Vaata detaile">
                    <i class="fas fa-eye"></i>
                  </a>
                </td>
              \`;

              tbody.appendChild(row);
            });
          }

          // Filter on change
          document.getElementById('status-filter').addEventListener('change', loadRequests);
        </script>
      </body>
      </html>
    `;

    return c.html(html);
  } catch (error) {
    console.error('Error loading partner requests page:', error);
    return c.text('Error loading page', 500);
  }
});
