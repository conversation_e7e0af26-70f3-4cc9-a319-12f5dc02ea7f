import { Hono } from 'hono';
import { html } from 'hono/html';
import { Machine } from '../models/Machine.js';
import { generateQRCodeDataURL } from '../utils/qrGenerator.js';

export const pageRoutes = new Hono();

// Root redirect to admin
pageRoutes.get('/', (c) => {
  return c.redirect('/admin');
});

// Admin dashboard
pageRoutes.get('/admin', async (c) => {
  try {
    const machines = await Machine.findAll();
    
    return c.html(html`
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Administraatori töölaud</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          <h1 class="text-3xl font-bold text-gray-800 mb-8">CMMS Administraatori töölaud</h1>
          
          <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-xl font-semibold">Masinad</h2>
              <a href="/machines/new" 
                 data-testid="add-machine-btn"
                 class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                Lisa uus masin
              </a>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              ${machines.map(machine => html`
                <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                  <h3 class="font-semibold">${machine.name}</h3>
                  <p class="text-gray-600">${machine.machine_number}</p>
                  <p class="text-sm text-gray-500">${machine.location || 'Asukoht määramata'}</p>
                  <div class="mt-2">
                    <span class="inline-block px-2 py-1 text-xs rounded ${
                      machine.status === 'online' ? 'bg-green-100 text-green-800' :
                      machine.status === 'offline' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }">
                      ${machine.status}
                    </span>
                  </div>
                  <a href="/machines/${machine.id}" 
                     class="inline-block mt-2 text-blue-500 hover:text-blue-600">
                    Vaata detaile →
                  </a>
                </div>
              `).join('')}
            </div>
          </div>
        </div>
      </body>
      </html>
    `);
  } catch (error) {
    console.error('Error loading admin dashboard:', error);
    return c.text('Error loading dashboard', 500);
  }
});

// Machine list
pageRoutes.get('/machines', async (c) => {
  try {
    const machines = await Machine.findAll();
    
    return c.html(html`
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Masinad</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          <h1 class="text-3xl font-bold text-gray-800 mb-8">Masinad</h1>
          
          <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <table class="w-full">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Masina number
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Nimi
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Asukoht
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Staatus
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Toimingud
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                ${machines.map(machine => html`
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      ${machine.machine_number}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${machine.name}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      ${machine.location || '-'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        machine.status === 'online' ? 'bg-green-100 text-green-800' :
                        machine.status === 'offline' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }">
                        ${machine.status}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <a href="/machines/${machine.id}" class="text-blue-600 hover:text-blue-900">
                        Vaata
                      </a>
                    </td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>
      </body>
      </html>
    `);
  } catch (error) {
    console.error('Error loading machines:', error);
    return c.text('Error loading machines', 500);
  }
});

// New machine form
pageRoutes.get('/machines/new', (c) => {
  return c.html(html`
    <!DOCTYPE html>
    <html lang="et">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>CMMS - Lisa uus masin</title>
      <script src="https://cdn.tailwindcss.com"></script>
    </head>
    <body class="bg-gray-100">
      <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">Lisa uus masin</h1>
        
        <div class="bg-white rounded-lg shadow-md p-6 max-w-2xl">
          <form action="/machines" method="POST" class="space-y-6">
            <div>
              <label for="machine_number" class="block text-sm font-medium text-gray-700">
                Masina number *
              </label>
              <input type="text" 
                     name="machine_number" 
                     id="machine_number"
                     required
                     class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
            </div>
            
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700">
                Nimi *
              </label>
              <input type="text" 
                     name="name" 
                     id="name"
                     required
                     class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="manufacturer" class="block text-sm font-medium text-gray-700">
                  Tootja
                </label>
                <input type="text" 
                       name="manufacturer" 
                       id="manufacturer"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>
              
              <div>
                <label for="model" class="block text-sm font-medium text-gray-700">
                  Mudel
                </label>
                <input type="text" 
                       name="model" 
                       id="model"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="department" class="block text-sm font-medium text-gray-700">
                  Osakond
                </label>
                <input type="text" 
                       name="department" 
                       id="department"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>
              
              <div>
                <label for="location" class="block text-sm font-medium text-gray-700">
                  Asukoht
                </label>
                <input type="text" 
                       name="location" 
                       id="location"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>
            </div>
            
            <div class="flex justify-end space-x-4">
              <a href="/admin" 
                 class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                Tühista
              </a>
              <button type="submit" 
                      class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                Lisa masin
              </button>
            </div>
          </form>
        </div>
      </div>
    </body>
    </html>
  `);
});

// Handle form submission for new machine
pageRoutes.post('/machines', async (c) => {
  try {
    const body = await c.req.parseBody();
    
    // Convert FormData to object
    const machineData = {};
    for (const [key, value] of Object.entries(body)) {
      machineData[key] = value;
    }
    
    const machine = await Machine.create(machineData);
    
    // Redirect to machine detail view with success message
    return c.redirect(`/machines/${machine.id}?success=created`);
  } catch (error) {
    console.error('Error creating machine:', error);
    
    // In a real app, we'd show the form again with error message
    return c.text(`Error creating machine: ${error.message}`, 500);
  }
});

// Machine detail view
pageRoutes.get('/machines/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const machine = await Machine.findById(id);
    
    if (!machine) {
      return c.text('Machine not found', 404);
    }
    
    const success = c.req.query('success');
    const qrCodeDataURL = await generateQRCodeDataURL(machine.machine_number);
    
    return c.html(html`
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - ${machine.name}</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          ${success === 'created' ? html`
            <div class="alert-success bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
              Masin edukalt lisatud
            </div>
          ` : ''}
          
          <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">
              <span data-testid="machine-name">${machine.name}</span>
              <span class="text-gray-500 text-lg">
                (<span data-testid="machine-number">${machine.machine_number}</span>)
              </span>
            </h1>
            <button data-testid="edit-machine" 
                    class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
              Muuda andmeid
            </button>
          </div>
          
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Machine details -->
            <div class="lg:col-span-2">
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Masina andmed</h2>
                <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Tootja</dt>
                    <dd class="mt-1 text-sm text-gray-900">${machine.manufacturer || '-'}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Mudel</dt>
                    <dd class="mt-1 text-sm text-gray-900">${machine.model || '-'}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Osakond</dt>
                    <dd class="mt-1 text-sm text-gray-900">${machine.department || '-'}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Asukoht</dt>
                    <dd class="mt-1 text-sm text-gray-900">${machine.location || '-'}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Staatus</dt>
                    <dd class="mt-1">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        machine.status === 'online' ? 'bg-green-100 text-green-800' :
                        machine.status === 'offline' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }">
                        ${machine.status}
                      </span>
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
            
            <!-- QR Code -->
            <div class="lg:col-span-1">
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">QR-kood</h2>
                <div class="text-center">
                  <img src="${qrCodeDataURL}" 
                       alt="QR kood masinale ${machine.machine_number}"
                       data-testid="qr-code"
                       class="mx-auto mb-4 border rounded">
                  <p class="text-sm text-gray-600 mb-4">
                    Skaneeri QR-kood operaatori vaate avamiseks
                  </p>
                  <a href="/api/files/qr/${machine.id}" 
                     download="machine_${machine.machine_number}_qr.png"
                     data-testid="download-qr"
                     class="inline-block bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded text-sm">
                    Laadi alla PNG
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </body>
      </html>
    `);
  } catch (error) {
    console.error('Error loading machine:', error);
    return c.text('Error loading machine', 500);
  }
});
