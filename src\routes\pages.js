import { Hono } from 'hono';
import { html } from 'hono/html';
import { Machine } from '../models/Machine.js';
import { generateQRCodeDataURL } from '../utils/qrGenerator.js';

export const pageRoutes = new Hono();

// Root redirect to admin
pageRoutes.get('/', (c) => {
  return c.redirect('/admin');
});

// Admin dashboard
pageRoutes.get('/admin', async (c) => {
  try {
    const machines = await Machine.findAll();

    const machinesHtml = machines.map(machine => `
      <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
        <h3 class="font-semibold">${machine.name}</h3>
        <p class="text-gray-600">${machine.machine_number}</p>
        <p class="text-sm text-gray-500">${machine.location || 'Asukoht määramata'}</p>
        <div class="mt-2">
          <span class="inline-block px-2 py-1 text-xs rounded ${
            machine.status === 'online' ? 'bg-green-100 text-green-800' :
            machine.status === 'offline' ? 'bg-red-100 text-red-800' :
            'bg-yellow-100 text-yellow-800'
          }">
            ${machine.status}
          </span>
        </div>
        <a href="/machines/${machine.id}"
           class="inline-block mt-2 text-blue-500 hover:text-blue-600">
          Vaata detaile →
        </a>
      </div>
    `).join('');

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Administraatori töölaud</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          <h1 class="text-3xl font-bold text-gray-800 mb-8">CMMS Administraatori töölaud</h1>

          <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-xl font-semibold">Masinad</h2>
              <div class="space-x-2">
                <button onclick="regenerateAllQRCodes()"
                        class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded text-sm">
                  Uuenda QR koodid
                </button>
                <a href="/machines/new"
                   data-testid="add-machine-btn"
                   class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                  Lisa uus masin
                </a>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              ${machinesHtml}
            </div>
          </div>
        </div>

        <script>
          async function regenerateAllQRCodes() {
            const button = event.target;
            const originalText = button.textContent;

            button.disabled = true;
            button.textContent = 'Uuendamine...';
            button.className = 'bg-gray-500 text-white px-4 py-2 rounded text-sm cursor-not-allowed';

            try {
              const response = await fetch('/api/machines/regenerate-all-qr', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                }
              });

              const result = await response.json();

              if (response.ok) {
                alert('QR koodid edukalt uuendatud: ' + result.message);
                // Refresh page to show updated QR codes
                window.location.reload();
              } else {
                alert('Viga QR koodide uuendamisel: ' + result.error);
              }
            } catch (error) {
              alert('Viga QR koodide uuendamisel: ' + error.message);
            } finally {
              button.disabled = false;
              button.textContent = originalText;
              button.className = 'bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded text-sm';
            }
          }
        </script>
      </body>
      </html>
    `;

    return new Response(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });
  } catch (error) {
    console.error('Error loading admin dashboard:', error);
    return c.text('Error loading dashboard', 500);
  }
});

// Machine list
pageRoutes.get('/machines', async (c) => {
  try {
    const machines = await Machine.findAll();

    return c.html(html`
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Masinad</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          <h1 class="text-3xl font-bold text-gray-800 mb-8">Masinad</h1>

          <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <table class="w-full">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Masina number
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Nimi
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Asukoht
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Staatus
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Toimingud
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                ${machines.map(machine => `
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      ${machine.machine_number}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${machine.name}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      ${machine.location || '-'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        machine.status === 'online' ? 'bg-green-100 text-green-800' :
                        machine.status === 'offline' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }">
                        ${machine.status}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <a href="/machines/${machine.id}" class="text-blue-600 hover:text-blue-900">
                        Vaata
                      </a>
                    </td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>
      </body>
      </html>
    `);
  } catch (error) {
    console.error('Error loading machines:', error);
    return c.text('Error loading machines', 500);
  }
});

// New machine form
pageRoutes.get('/machines/new', (c) => {
  return c.html(html`
    <!DOCTYPE html>
    <html lang="et">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>CMMS - Lisa uus masin</title>
      <script src="https://cdn.tailwindcss.com"></script>
    </head>
    <body class="bg-gray-100">
      <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">Lisa uus masin</h1>

        <div class="bg-white rounded-lg shadow-md p-6 max-w-2xl">
          <form action="/machines" method="POST" class="space-y-6">
            <div>
              <label for="machine_number" class="block text-sm font-medium text-gray-700">
                Masina number *
              </label>
              <input type="text"
                     name="machine_number"
                     id="machine_number"
                     required
                     class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
            </div>

            <div>
              <label for="name" class="block text-sm font-medium text-gray-700">
                Nimi *
              </label>
              <input type="text"
                     name="name"
                     id="name"
                     required
                     class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="manufacturer" class="block text-sm font-medium text-gray-700">
                  Tootja
                </label>
                <input type="text"
                       name="manufacturer"
                       id="manufacturer"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>

              <div>
                <label for="model" class="block text-sm font-medium text-gray-700">
                  Mudel
                </label>
                <input type="text"
                       name="model"
                       id="model"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="department" class="block text-sm font-medium text-gray-700">
                  Osakond
                </label>
                <input type="text"
                       name="department"
                       id="department"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>

              <div>
                <label for="location" class="block text-sm font-medium text-gray-700">
                  Asukoht
                </label>
                <input type="text"
                       name="location"
                       id="location"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>
            </div>

            <div class="flex justify-end space-x-4">
              <a href="/admin"
                 class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                Tühista
              </a>
              <button type="submit"
                      class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                Lisa masin
              </button>
            </div>
          </form>
        </div>
      </div>
    </body>
    </html>
  `);
});

// Handle form submission for new machine
pageRoutes.post('/machines', async (c) => {
  try {
    const body = await c.req.parseBody();

    // Convert FormData to object
    const machineData = {};
    for (const [key, value] of Object.entries(body)) {
      machineData[key] = value;
    }

    const machine = await Machine.create(machineData);

    // Redirect to machine detail view with success message
    return c.redirect(`/machines/${machine.id}?success=created`);
  } catch (error) {
    console.error('Error creating machine:', error);

    // In a real app, we'd show the form again with error message
    return c.text(`Error creating machine: ${error.message}`, 500);
  }
});

// Operator view - mobile-friendly interface accessed via QR code
pageRoutes.get('/operator/:machineNumber', async (c) => {
  try {
    const machineNumber = c.req.param('machineNumber');
    const machine = await Machine.findByMachineNumber(machineNumber);

    if (!machine) {
      return c.html(html`
        <!DOCTYPE html>
        <html lang="et">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>CMMS - Masinat ei leitud</title>
          <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100 min-h-screen flex items-center justify-center">
          <div class="max-w-md w-full mx-4">
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
              <div class="alert-error bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                Masinat ei leitud
              </div>
              <div data-testid="error-message" class="text-gray-600 mb-4">
                Masina number "${machineNumber}" ei ole süsteemis registreeritud.
              </div>
              <a href="/admin" class="inline-block bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                Tagasi administraatori vaatesse
              </a>
            </div>
          </div>
        </body>
        </html>
      `, 404);
    }

    return c.html(html`
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - ${machine.name}</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
          /* Ensure mobile-friendly touch targets */
          button, input[type="submit"], input[type="button"] {
            min-height: 44px;
          }
        </style>
      </head>
      <body class="bg-gray-100 min-h-screen">
        <div class="container max-w-md mx-auto px-4 py-6">
          <!-- Machine Information -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div data-testid="machine-info" class="text-center mb-4">
              <h1 class="text-2xl font-bold text-gray-800 mb-2">
                <span data-testid="machine-name">${machine.name}</span>
              </h1>
              <p class="text-lg text-gray-600 mb-2">
                <span data-testid="machine-number">${machine.machine_number}</span>
              </p>
              <div class="flex justify-center items-center space-x-4 text-sm">
                <span data-testid="machine-status" class="inline-flex px-3 py-1 rounded-full text-xs font-semibold ${
                  machine.status === 'online' ? 'bg-green-100 text-green-800' :
                  machine.status === 'offline' ? 'bg-red-100 text-red-800' :
                  'bg-yellow-100 text-yellow-800'
                }">
                  ${machine.status}
                </span>
                ${machine.location ? html`
                  <span data-testid="machine-location" class="text-gray-500">
                    📍 ${machine.location}
                  </span>
                ` : ''}
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="space-y-4">
            <a href="/operator/${machine.machine_number}/issue"
               class="block w-full bg-red-500 hover:bg-red-600 text-white text-center py-4 px-6 rounded-lg font-semibold text-lg">
              🚨 Teata rikest
            </a>

            <a href="/operator/${machine.machine_number}/maintenance"
               class="block w-full bg-orange-500 hover:bg-orange-600 text-white text-center py-4 px-6 rounded-lg font-semibold text-lg">
              🔧 Teata hoolduse vajadusest
            </a>

            <a href="/machines/${machine.id}"
               class="block w-full bg-blue-500 hover:bg-blue-600 text-white text-center py-4 px-6 rounded-lg font-semibold text-lg">
              📋 Vaata masina detaile
            </a>
          </div>

          <!-- Operator Number Input (for future issue reporting) -->
          <div class="mt-8 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold mb-4">Operaatori andmed</h2>
            <form class="space-y-4">
              <div>
                <label for="operator_number" class="block text-sm font-medium text-gray-700 mb-2">
                  Operaatori number
                </label>
                <input type="text"
                       name="operator_number"
                       id="operator_number"
                       placeholder="OP-123"
                       class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-lg"
                       autofocus>
              </div>
            </form>
          </div>

          <!-- Footer -->
          <div class="mt-8 text-center text-sm text-gray-500">
            <p>CMMS - Computerized Maintenance Management System</p>
          </div>
        </div>

        <script>
          // Progressive enhancement: Auto-focus first input
          document.addEventListener('DOMContentLoaded', function() {
            const operatorInput = document.querySelector('[name="operator_number"]');
            if (operatorInput) {
              operatorInput.focus();
            }
          });
        </script>
      </body>
      </html>
    `);
  } catch (error) {
    console.error('Error loading operator view:', error);
    return c.text('Error loading operator view', 500);
  }
});

// Machine detail view
pageRoutes.get('/machines/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const machine = await Machine.findById(id);

    if (!machine) {
      return c.text('Machine not found', 404);
    }

    const success = c.req.query('success');
    const qrCodeDataURL = await generateQRCodeDataURL(machine.machine_number);

    return c.html(html`
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - ${machine.name}</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          ${success === 'created' ? html`
            <div class="alert-success bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
              Masin edukalt lisatud
            </div>
          ` : success === 'updated' ? html`
            <div class="alert-success bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
              Andmed edukalt uuendatud
            </div>
          ` : ''}

          <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">
              <span data-testid="machine-name">${machine.name}</span>
              <span class="text-gray-500 text-lg">
                (<span data-testid="machine-number">${machine.machine_number}</span>)
              </span>
            </h1>
            <a href="/machines/${machine.id}/edit"
               data-testid="edit-machine"
               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded inline-block">
              Muuda andmeid
            </a>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Machine details -->
            <div class="lg:col-span-2">
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Masina andmed</h2>
                <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Tootja</dt>
                    <dd class="mt-1 text-sm text-gray-900">${machine.manufacturer || '-'}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Mudel</dt>
                    <dd class="mt-1 text-sm text-gray-900">${machine.model || '-'}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Osakond</dt>
                    <dd class="mt-1 text-sm text-gray-900">${machine.department || '-'}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Asukoht</dt>
                    <dd class="mt-1 text-sm text-gray-900">${machine.location || '-'}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Staatus</dt>
                    <dd class="mt-1">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        machine.status === 'online' ? 'bg-green-100 text-green-800' :
                        machine.status === 'offline' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }">
                        ${machine.status}
                      </span>
                    </dd>
                  </div>
                </dl>
              </div>
            </div>

            <!-- QR Code -->
            <div class="lg:col-span-1">
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">QR-kood</h2>
                <div class="text-center">
                  <img src="${qrCodeDataURL}"
                       alt="QR kood masinale ${machine.machine_number}"
                       data-testid="qr-code"
                       class="mx-auto mb-4 border rounded">
                  <p class="text-sm text-gray-600 mb-4">
                    Skaneeri QR-kood operaatori vaate avamiseks
                  </p>
                  <a href="/api/files/qr/${machine.id}"
                     download="machine_${machine.machine_number}_qr.png"
                     data-testid="download-qr"
                     class="inline-block bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded text-sm">
                    Laadi alla PNG
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </body>
      </html>
    `);
  } catch (error) {
    console.error('Error loading machine:', error);
    return c.text('Error loading machine', 500);
  }
});

// Machine edit form
pageRoutes.get('/machines/:id/edit', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const machine = await Machine.findById(id);

    if (!machine) {
      return c.text('Machine not found', 404);
    }

    return c.html(html`
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Muuda masinat: ${machine.name}</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          <h1 class="text-3xl font-bold text-gray-800 mb-8">Muuda masinat: ${machine.name}</h1>

          <div class="bg-white rounded-lg shadow-md p-6 max-w-2xl">
            <form action="/machines/${machine.id}" method="POST" class="space-y-6">
              <input type="hidden" name="_method" value="PUT">

              <div>
                <label for="machine_number" class="block text-sm font-medium text-gray-700">
                  Masina number *
                </label>
                <input type="text"
                       name="machine_number"
                       id="machine_number"
                       value="${machine.machine_number}"
                       required
                       readonly
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500">
                <p class="text-xs text-gray-500 mt-1">Masina numbrit ei saa muuta</p>
              </div>

              <div>
                <label for="name" class="block text-sm font-medium text-gray-700">
                  Nimi *
                </label>
                <input type="text"
                       name="name"
                       id="name"
                       value="${machine.name}"
                       required
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="manufacturer" class="block text-sm font-medium text-gray-700">
                    Tootja
                  </label>
                  <input type="text"
                         name="manufacturer"
                         id="manufacturer"
                         value="${machine.manufacturer || ''}"
                         class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                  <label for="model" class="block text-sm font-medium text-gray-700">
                    Mudel
                  </label>
                  <input type="text"
                         name="model"
                         id="model"
                         value="${machine.model || ''}"
                         class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="department" class="block text-sm font-medium text-gray-700">
                    Osakond
                  </label>
                  <input type="text"
                         name="department"
                         id="department"
                         value="${machine.department || ''}"
                         class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                  <label for="location" class="block text-sm font-medium text-gray-700">
                    Asukoht
                  </label>
                  <input type="text"
                         name="location"
                         id="location"
                         value="${machine.location || ''}"
                         class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>
              </div>

              <div>
                <label for="status" class="block text-sm font-medium text-gray-700">
                  Staatus
                </label>
                <select name="status"
                        id="status"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                  <option value="online" ${machine.status === 'online' ? 'selected' : ''}>Online</option>
                  <option value="offline" ${machine.status === 'offline' ? 'selected' : ''}>Offline</option>
                  <option value="maintenance" ${machine.status === 'maintenance' ? 'selected' : ''}>Hoolduses</option>
                </select>
              </div>

              <div class="flex justify-end space-x-4">
                <a href="/machines/${machine.id}"
                   class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                  Tühista
                </a>
                <button type="submit"
                        class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                  Salvesta muudatused
                </button>
              </div>
            </form>
          </div>
        </div>
      </body>
      </html>
    `);
  } catch (error) {
    console.error('Error loading machine edit form:', error);
    return c.text('Error loading edit form', 500);
  }
});

// Handle machine update
pageRoutes.post('/machines/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const body = await c.req.parseBody();

    // Convert FormData to object, excluding _method
    const updateData = {};
    for (const [key, value] of Object.entries(body)) {
      if (key !== '_method') {
        updateData[key] = value;
      }
    }

    const machine = await Machine.update(id, updateData);

    if (!machine) {
      return c.text('Machine not found', 404);
    }

    // Redirect to machine detail view with success message
    return c.redirect(`/machines/${machine.id}?success=updated`);
  } catch (error) {
    console.error('Error updating machine:', error);
    return c.text(`Error updating machine: ${error.message}`, 500);
  }
});
