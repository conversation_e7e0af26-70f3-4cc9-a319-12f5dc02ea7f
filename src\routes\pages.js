import { Hono } from 'hono';
import { html } from 'hono/html';
import { Machine } from '../models/Machine.js';
import { MachineGroup } from '../models/MachineGroup.js';
import { Issue } from '../models/Issue.js';
import { MaintenanceRequest } from '../models/MaintenanceRequest.js';
import Part from '../models/Part.js';
import MachinePart from '../models/MachinePart.js';
import { generateQRCodeDataURL } from '../utils/qrGenerator.js';

export const pageRoutes = new Hono();

// Root redirect to admin
pageRoutes.get('/', (c) => {
  return c.redirect('/admin');
});

// Admin dashboard
pageRoutes.get('/admin', async (c) => {
  try {
    const query = c.req.query();
    const selectedGroupId = query.group_id ? parseInt(query.group_id) : null;

    // Filter machines by selected group if specified
    const machineFilters = {};
    if (selectedGroupId) {
      machineFilters.group_id = selectedGroupId;
    }

    const machines = await Machine.findAll(machineFilters);
    const issueStats = await Issue.getStatistics();
    const maintenanceStats = await MaintenanceRequest.getStatistics();
    const groupStats = await MachineGroup.getStatistics();

    // Parts statistics with error handling
    let partsStats = { total_parts: 0, total_inventory_value: 0, low_stock_count: 0, out_of_stock_count: 0 };
    let lowStockParts = [];

    try {
      partsStats = await Part.getStatistics();
      lowStockParts = await Part.findLowStock();
    } catch (error) {
      console.error('Error loading parts data:', error);
    }

    const groups = await MachineGroup.findAll({ is_active: true });
    const selectedGroup = selectedGroupId ? groups.find(g => g.id === selectedGroupId) : null;

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Administraatori töölaud</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
      </head>
      <body class="bg-gray-50">
        <div class="min-h-screen">
          <!-- Navigation -->
          <nav class="bg-blue-600 text-white">
            <div class="container mx-auto px-4">
              <!-- Desktop Navigation -->
              <div class="hidden md:flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                  <h1 class="text-xl font-bold">CMMS - Dashboard</h1>
                </div>
                <div class="flex space-x-4">
                  <a href="/admin" class="bg-blue-800 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                  </a>
                  <a href="/machines" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-cogs mr-2"></i>Masinad
                  </a>
                  <a href="/admin/machine-groups" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-layer-group mr-2"></i>Grupid
                  </a>
                  <a href="/admin/reports" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-chart-bar mr-2"></i>Aruanded
                  </a>
                  <a href="/admin/issues" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Rikked
                  </a>
                  <a href="/admin/maintenance" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-wrench mr-2"></i>Hooldus
                  </a>
                  <a href="/admin/partners" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-handshake mr-2"></i>Partnerid
                  </a>
                  <a href="/admin/parts" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-boxes mr-2"></i>Varuosad
                  </a>
                </div>
              </div>

              <!-- Mobile Navigation -->
              <div class="md:hidden">
                <div class="flex justify-between items-center py-3">
                  <h1 class="text-lg font-bold">CMMS</h1>
                  <button onclick="toggleMobileMenu()" class="p-2 rounded hover:bg-blue-700 min-h-[44px] min-w-[44px]">
                    <i id="mobile-menu-icon" class="fas fa-bars text-xl"></i>
                  </button>
                </div>

                <!-- Mobile Menu -->
                <div id="mobile-menu" class="hidden pb-4">
                  <div class="space-y-2">
                    <a href="/admin" class="block bg-blue-800 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-home mr-2"></i>Dashboard
                    </a>
                    <a href="/machines" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-cogs mr-2"></i>Masinad
                    </a>
                    <a href="/admin/machine-groups" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-layer-group mr-2"></i>Grupid
                    </a>
                    <a href="/admin/reports" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-chart-bar mr-2"></i>Aruanded
                    </a>
                    <a href="/admin/issues" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-exclamation-triangle mr-2"></i>Rikked
                    </a>
                    <a href="/admin/maintenance" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-wrench mr-2"></i>Hooldus
                    </a>
                    <a href="/admin/partners" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-handshake mr-2"></i>Partnerid
                    </a>
                    <a href="/admin/parts" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-boxes mr-2"></i>Varuosad
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </nav>

          <div class="container mx-auto px-4 py-8">
            <!-- Mobile-friendly header -->
            <div class="mb-6">
              <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
                <h2 class="text-xl sm:text-2xl font-bold text-gray-800">
                  Dashboard
                  ${selectedGroup ? `<br><span class="text-lg text-blue-600">${selectedGroup.name}</span>` : ''}
                </h2>
                <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                  <label for="group-filter" class="text-sm font-medium text-gray-700 sm:whitespace-nowrap">Filtreeri grupi järgi:</label>
                  <select id="group-filter" onchange="filterByGroup(this.value)"
                          class="border border-gray-300 rounded-md px-3 py-3 text-sm w-full sm:min-w-48 min-h-[44px]">
                    <option value="">Kõik grupid</option>
                    ${groups.map(group => `
                      <option value="${group.id}" ${selectedGroupId == group.id ? 'selected' : ''}>
                        ${group.name} (${group.machine_count})
                      </option>
                    `).join('')}
                  </select>
                </div>
              </div>
            </div>

          <!-- Issue Statistics -->
          <div class="mb-6">
            <h2 class="text-lg sm:text-xl font-semibold text-gray-800 mb-4">📋 Rikete ülevaade</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                  <i class="fas fa-clipboard-list text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Kokku rikked</p>
                  <p class="text-2xl font-semibold text-gray-900">${issueStats.total_issues || 0}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                  <i class="fas fa-exclamation-triangle text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Avatud rikked</p>
                  <p class="text-2xl font-semibold text-gray-900">${issueStats.open_issues || 0}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                  <i class="fas fa-tools text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Töös</p>
                  <p class="text-2xl font-semibold text-gray-900">${issueStats.in_progress_issues || 0}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                  <i class="fas fa-fire text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Kriitilised</p>
                  <p class="text-2xl font-semibold text-gray-900">${issueStats.critical_issues || 0}</p>
                </div>
              </div>
            </div>
          </div>
          </div>

          <!-- Maintenance Statistics -->
          <div class="mb-6">
            <h2 class="text-lg sm:text-xl font-semibold text-gray-800 mb-4">🔧 Hoolduse ülevaade</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                  <i class="fas fa-wrench text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Kokku taotlused</p>
                  <p class="text-2xl font-semibold text-gray-900">${maintenanceStats.total_requests || 0}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                  <i class="fas fa-clock text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Ootel</p>
                  <p class="text-2xl font-semibold text-gray-900">${maintenanceStats.pending_requests || 0}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                  <i class="fas fa-calendar text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Planeeritud</p>
                  <p class="text-2xl font-semibold text-gray-900">${maintenanceStats.scheduled_requests || 0}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                  <i class="fas fa-bolt text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Kiireloomulised</p>
                  <p class="text-2xl font-semibold text-gray-900">${maintenanceStats.urgent_requests || 0}</p>
                </div>
              </div>
            </div>
          </div>
          </div>

          <!-- Machine Groups Statistics -->
          <div class="mb-6">
            <h2 class="text-lg sm:text-xl font-semibold text-gray-800 mb-4">🏷️ Masinate grupid</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                  <i class="fas fa-layer-group text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Kokku Gruppe</p>
                  <p class="text-2xl font-semibold text-gray-900">${groupStats.total_groups || 0}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                  <i class="fas fa-check-circle text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Aktiivsed</p>
                  <p class="text-2xl font-semibold text-gray-900">${groupStats.active_groups || 0}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                  <i class="fas fa-cogs text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Masinad Gruppides</p>
                  <p class="text-2xl font-semibold text-gray-900">${groupStats.total_machines_in_groups || 0}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                  <i class="fas fa-exclamation-triangle text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Ilma Grupita</p>
                  <p class="text-2xl font-semibold text-gray-900">${groupStats.ungrouped_machines || 0}</p>
                </div>
              </div>
            </div>
          </div>
          </div>

          <!-- Parts Statistics -->
          <div class="mb-6">
            <h2 class="text-lg sm:text-xl font-semibold text-gray-800 mb-4">📦 Varuosade ülevaade</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                  <i class="fas fa-boxes text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Kokku Varuosad</p>
                  <p class="text-2xl font-semibold text-gray-900">${partsStats.total_parts || 0}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                  <i class="fas fa-euro-sign text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Lao Väärtus</p>
                  <p class="text-2xl font-semibold text-gray-900">€${parseFloat(partsStats.total_inventory_value || 0).toFixed(2)}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                  <i class="fas fa-exclamation-triangle text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Madal Varu</p>
                  <p class="text-2xl font-semibold text-gray-900">${partsStats.low_stock_count || 0}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                  <i class="fas fa-times-circle text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Otsas</p>
                  <p class="text-2xl font-semibold text-gray-900">${partsStats.out_of_stock_count || 0}</p>
                </div>
              </div>
            </div>
          </div>
          </div>

          <!-- Low Stock Parts Alert -->
          ${lowStockParts.length > 0 ? `
          <div class="bg-orange-50 border-l-4 border-orange-400 p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-orange-400"></i>
              </div>
              <div class="ml-3">
                <p class="text-sm text-orange-700">
                  <strong>Hoiatus:</strong> ${lowStockParts.length} varuosal on madal laoseis!
                  <a href="/admin/parts?low_stock=true" class="font-medium underline hover:text-orange-600">
                    Vaata madala varuga varuosasid →
                  </a>
                </p>
              </div>
            </div>
          </div>
          ` : ''}

          <!-- Groups Overview -->
          <div class="bg-white rounded-lg shadow p-4 sm:p-6 mb-8">
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-4">
              <h2 class="text-lg sm:text-xl font-semibold">Gruppide ülevaade</h2>
              <a href="/admin/machine-groups" class="bg-blue-500 text-white px-4 py-3 rounded hover:bg-blue-600 text-center min-h-[44px] flex items-center justify-center">
                <i class="fas fa-layer-group mr-2"></i>Vaata kõiki gruppe
              </a>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
              ${groups.map(group => `
                <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div class="flex items-center justify-between mb-2">
                    <div class="flex items-center">
                      <div class="w-4 h-4 rounded-full mr-2" style="background-color: ${group.color}"></div>
                      <h3 class="font-semibold text-gray-900">${group.name}</h3>
                    </div>
                    <span class="text-sm text-gray-500">${group.machine_count} masinat</span>
                  </div>
                  ${group.description ? `<p class="text-sm text-gray-600 mb-2">${group.description}</p>` : ''}
                  <div class="flex justify-between items-center">
                    <span class="text-xs px-2 py-1 rounded-full ${group.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                      ${group.is_active ? 'Aktiivne' : 'Mitteaktiivne'}
                    </span>
                    <a href="/machines?group_id=${group.id}" class="text-blue-600 hover:text-blue-800 text-sm">
                      Vaata masinaid →
                    </a>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>

          <!-- Machine Groups Chart (Collapsible) -->
          <div class="bg-white rounded-lg shadow mb-8">
            <div class="p-4 border-b border-gray-200">
              <button onclick="toggleChart()" class="flex items-center justify-between w-full text-left">
                <h2 class="text-lg font-semibold text-gray-800">📊 Masinate jaotus gruppide järgi</h2>
                <i id="chart-toggle-icon" class="fas fa-chevron-down text-gray-500 transition-transform"></i>
              </button>
            </div>
            <div id="chart-content" class="hidden p-6">
              <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="lg:col-span-1">
                  <canvas id="groupsChart" width="300" height="300"></canvas>
                </div>
                <div class="lg:col-span-2 space-y-2">
                  <h3 class="font-semibold text-gray-800 mb-3">Gruppide detailid:</h3>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    ${groups.map(group => `
                      <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                        <div class="flex items-center">
                          <div class="w-3 h-3 rounded-full mr-2" style="background-color: ${group.color}"></div>
                          <span class="text-sm font-medium">${group.name}</span>
                        </div>
                        <div class="text-right">
                          <div class="text-sm font-semibold">${group.machine_count}</div>
                          <div class="text-xs text-gray-500">${((group.machine_count / (groupStats.total_machines_in_groups || 1)) * 100).toFixed(1)}%</div>
                        </div>
                      </div>
                    `).join('')}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Issues and Maintenance -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Recent Issues -->
            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-800">🚨 Viimased rikked</h2>
                <a href="/admin/issues" class="text-blue-600 hover:text-blue-800 text-sm">
                  Vaata kõiki →
                </a>
              </div>
              <div class="space-y-3">
                <div class="p-3 bg-red-50 border-l-4 border-red-400 rounded">
                  <div class="flex justify-between items-start">
                    <div>
                      <p class="font-medium text-red-800">Kriitilised rikked vajavad tähelepanu</p>
                      <p class="text-sm text-red-600">Kokku ${issueStats.critical_issues || 0} kriitilise prioriteediga riket</p>
                    </div>
                    <span class="text-xs text-red-500">Täna</span>
                  </div>
                </div>
                <div class="p-3 bg-yellow-50 border-l-4 border-yellow-400 rounded">
                  <div class="flex justify-between items-start">
                    <div>
                      <p class="font-medium text-yellow-800">Töös olevad rikked</p>
                      <p class="text-sm text-yellow-600">${issueStats.in_progress_issues || 0} riket on hetkel lahendamisel</p>
                    </div>
                    <span class="text-xs text-yellow-500">Käimasolev</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Recent Maintenance -->
            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-800">🔧 Hoolduse tegevused</h2>
                <a href="/admin/maintenance" class="text-blue-600 hover:text-blue-800 text-sm">
                  Vaata kõiki →
                </a>
              </div>
              <div class="space-y-3">
                <div class="p-3 bg-purple-50 border-l-4 border-purple-400 rounded">
                  <div class="flex justify-between items-start">
                    <div>
                      <p class="font-medium text-purple-800">Kiireloomulised hooldused</p>
                      <p class="text-sm text-purple-600">${maintenanceStats.urgent_requests || 0} kiireloomulist hooldust ootab</p>
                    </div>
                    <span class="text-xs text-purple-500">Tähtis</span>
                  </div>
                </div>
                <div class="p-3 bg-blue-50 border-l-4 border-blue-400 rounded">
                  <div class="flex justify-between items-start">
                    <div>
                      <p class="font-medium text-blue-800">Planeeritud hooldused</p>
                      <p class="text-sm text-blue-600">${maintenanceStats.scheduled_requests || 0} hooldust on planeeritud</p>
                    </div>
                    <span class="text-xs text-blue-500">Planeeritud</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Access to Machines -->
          <div class="bg-white rounded-lg shadow p-4 sm:p-6 mb-8">
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-4">
              <h2 class="text-lg sm:text-xl font-semibold">
                🏭 Masinate ülevaade (${machines.length} masinat)
              </h2>
              <div class="flex flex-col sm:flex-row gap-2">
                <a href="/machines"
                   class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-3 rounded text-sm min-h-[44px] flex items-center justify-center">
                  <i class="fas fa-list mr-1"></i> Halda kõiki masinaid
                </a>
              </div>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
              <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div class="flex items-center">
                  <div class="p-2 bg-green-100 rounded-full">
                    <i class="fas fa-check-circle text-green-600"></i>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">Online</p>
                    <p class="text-lg font-semibold text-green-900">${machines.filter(m => m.status === 'online').length}</p>
                  </div>
                </div>
              </div>
              <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center">
                  <div class="p-2 bg-red-100 rounded-full">
                    <i class="fas fa-times-circle text-red-600"></i>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-red-800">Offline</p>
                    <p class="text-lg font-semibold text-red-900">${machines.filter(m => m.status === 'offline').length}</p>
                  </div>
                </div>
              </div>
              <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-center">
                  <div class="p-2 bg-yellow-100 rounded-full">
                    <i class="fas fa-wrench text-yellow-600"></i>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-yellow-800">Hoolduses</p>
                    <p class="text-lg font-semibold text-yellow-900">${machines.filter(m => m.status === 'maintenance').length}</p>
                  </div>
                </div>
              </div>
              <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div class="flex items-center">
                  <div class="p-2 bg-blue-100 rounded-full">
                    <i class="fas fa-cogs text-blue-600"></i>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-blue-800">Kokku</p>
                    <p class="text-lg font-semibold text-blue-900">${machines.length}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>

        <script>
          // Mobile menu toggle
          function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            const icon = document.getElementById('mobile-menu-icon');

            if (menu.classList.contains('hidden')) {
              menu.classList.remove('hidden');
              icon.classList.remove('fa-bars');
              icon.classList.add('fa-times');
            } else {
              menu.classList.add('hidden');
              icon.classList.remove('fa-times');
              icon.classList.add('fa-bars');
            }
          }

          // Close mobile menu when clicking outside
          document.addEventListener('click', function(event) {
            const menu = document.getElementById('mobile-menu');
            const button = event.target.closest('button[onclick="toggleMobileMenu()"]');

            if (!button && !menu.contains(event.target) && !menu.classList.contains('hidden')) {
              toggleMobileMenu();
            }
          });

          // Group filter function
          function filterByGroup(groupId) {
            const url = new URL(window.location);
            if (groupId) {
              url.searchParams.set('group_id', groupId);
            } else {
              url.searchParams.delete('group_id');
            }
            window.location.href = url.toString();
          }

          // Toggle chart visibility
          function toggleChart() {
            const content = document.getElementById('chart-content');
            const icon = document.getElementById('chart-toggle-icon');

            if (content.classList.contains('hidden')) {
              content.classList.remove('hidden');
              icon.classList.add('rotate-180');

              // Initialize chart when opened for the first time
              if (!window.chartInitialized) {
                initializeChart();
                window.chartInitialized = true;
              }
            } else {
              content.classList.add('hidden');
              icon.classList.remove('rotate-180');
            }
          }

          // Initialize Groups Chart
          function initializeChart() {
            const ctx = document.getElementById('groupsChart').getContext('2d');
            const groupsData = ${JSON.stringify(groups.filter(g => g.machine_count > 0))};

            new Chart(ctx, {
              type: 'doughnut',
              data: {
                labels: groupsData.map(g => g.name),
                datasets: [{
                  data: groupsData.map(g => g.machine_count),
                  backgroundColor: groupsData.map(g => g.color),
                  borderWidth: 2,
                  borderColor: '#ffffff'
                }]
              },
              options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                  legend: {
                    position: 'bottom',
                    labels: {
                      padding: 20,
                      usePointStyle: true
                    }
                  },
                  tooltip: {
                    callbacks: {
                      label: function(context) {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                        return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                      }
                    }
                  }
                }
              }
            });
          }

          async function regenerateAllQRCodes() {
            const button = event.target;
            const originalText = button.textContent;

            button.disabled = true;
            button.textContent = 'Uuendamine...';
            button.className = 'bg-gray-500 text-white px-4 py-2 rounded text-sm cursor-not-allowed';

            try {
              const response = await fetch('/api/machines/regenerate-all-qr', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                }
              });

              const result = await response.json();

              if (response.ok) {
                alert('QR koodid edukalt uuendatud: ' + result.message);
                // Refresh page to show updated QR codes
                window.location.reload();
              } else {
                alert('Viga QR koodide uuendamisel: ' + result.error);
              }
            } catch (error) {
              alert('Viga QR koodide uuendamisel: ' + error.message);
            } finally {
              button.disabled = false;
              button.textContent = originalText;
              button.className = 'bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded text-sm';
            }
          }
        </script>
      </body>
      </html>
    `;

    return new Response(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });
  } catch (error) {
    console.error('Error loading admin dashboard:', error);
    return c.text('Error loading dashboard', 500);
  }
});

// Machine list
pageRoutes.get('/machines', async (c) => {
  try {
    const query = c.req.query();
    const filters = {};

    if (query.group_id) {
      filters.group_id = parseInt(query.group_id);
    }

    if (query.status) {
      filters.status = query.status;
    }

    if (query.search) {
      filters.search = query.search;
    }

    const machines = await Machine.findAll(filters);
    const groups = await MachineGroup.findAll({ is_active: true });

    const machinesTableRows = machines.map(machine => `
      <tr>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
          ${machine.machine_number}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          ${machine.name}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${machine.group_name ?
            '<div class="flex items-center">' +
              '<div class="w-3 h-3 rounded-full mr-2" style="background-color: ' + machine.group_color + '"></div>' +
              '<span>' + machine.group_name + '</span>' +
            '</div>'
            : '-'}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${machine.location || '-'}
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            machine.status === 'online' ? 'bg-green-100 text-green-800' :
            machine.status === 'offline' ? 'bg-red-100 text-red-800' :
            'bg-yellow-100 text-yellow-800'
          }">
            ${machine.status}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <a href="/machines/${machine.id}" class="text-blue-600 hover:text-blue-900">
            Vaata
          </a>
        </td>
      </tr>
    `).join('');

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Masinad</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
      </head>
      <body class="bg-gray-50">
        <div class="min-h-screen">
          <!-- Navigation -->
          <nav class="bg-blue-600 text-white">
            <div class="container mx-auto px-4">
              <!-- Desktop Navigation -->
              <div class="hidden md:flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                  <h1 class="text-xl font-bold">CMMS - Masinad</h1>
                </div>
                <div class="flex space-x-4">
                  <a href="/admin" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                  </a>
                  <a href="/machines" class="bg-blue-800 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-cogs mr-2"></i>Masinad
                  </a>
                  <a href="/admin/machine-groups" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-layer-group mr-2"></i>Grupid
                  </a>
                  <a href="/admin/reports" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-chart-bar mr-2"></i>Aruanded
                  </a>
                  <a href="/admin/issues" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Rikked
                  </a>
                  <a href="/admin/maintenance" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-wrench mr-2"></i>Hooldus
                  </a>
                  <a href="/admin/partners" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-handshake mr-2"></i>Partnerid
                  </a>
                </div>
              </div>

              <!-- Mobile Navigation -->
              <div class="md:hidden">
                <div class="flex justify-between items-center py-3">
                  <h1 class="text-lg font-bold">CMMS - Masinad</h1>
                  <button onclick="toggleMobileMenu()" class="p-2 rounded hover:bg-blue-700 min-h-[44px] min-w-[44px]">
                    <i id="mobile-menu-icon" class="fas fa-bars text-xl"></i>
                  </button>
                </div>

                <!-- Mobile Menu -->
                <div id="mobile-menu" class="hidden pb-4">
                  <div class="space-y-2">
                    <a href="/admin" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-home mr-2"></i>Dashboard
                    </a>
                    <a href="/machines" class="block bg-blue-800 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-cogs mr-2"></i>Masinad
                    </a>
                    <a href="/admin/machine-groups" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-layer-group mr-2"></i>Grupid
                    </a>
                    <a href="/admin/reports" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-chart-bar mr-2"></i>Aruanded
                    </a>
                    <a href="/admin/issues" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-exclamation-triangle mr-2"></i>Rikked
                    </a>
                    <a href="/admin/maintenance" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-wrench mr-2"></i>Hooldus
                    </a>
                    <a href="/admin/partners" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-handshake mr-2"></i>Partnerid
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </nav>

          <div class="container mx-auto px-4 py-6 sm:py-8">
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
              <h2 class="text-xl sm:text-2xl font-bold text-gray-800">Masinad</h2>
              <div class="flex flex-col sm:flex-row gap-2 sm:gap-3">
                <button onclick="regenerateAllQRCodes()" class="bg-green-600 text-white px-4 py-3 rounded hover:bg-green-700 text-center min-h-[44px] flex items-center justify-center">
                  <i class="fas fa-qrcode mr-2"></i>Uuenda QR
                </button>
                <a href="/machines/new" class="bg-blue-600 text-white px-4 py-3 rounded hover:bg-blue-700 text-center min-h-[44px] flex items-center justify-center">
                  <i class="fas fa-plus mr-2"></i>Lisa Masin
                </a>
              </div>
            </div>

            <!-- Filters -->
            <div class="bg-white rounded-lg shadow p-4 mb-6">
              <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label for="group-filter" class="block text-sm font-medium text-gray-700 mb-1">Grupp</label>
                  <select id="group-filter" class="w-full border border-gray-300 rounded-md px-3 py-3 text-sm min-h-[44px]">
                    <option value="">Kõik grupid</option>
                    ${groups.map(group => `
                      <option value="${group.id}" ${query.group_id == group.id ? 'selected' : ''}>
                        ${group.name} (${group.machine_count})
                      </option>
                    `).join('')}
                  </select>
                </div>

                <div>
                  <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">Staatus</label>
                  <select id="status-filter" class="w-full border border-gray-300 rounded-md px-3 py-3 text-sm min-h-[44px]">
                    <option value="">Kõik staatused</option>
                    <option value="online" ${query.status === 'online' ? 'selected' : ''}>Online</option>
                    <option value="offline" ${query.status === 'offline' ? 'selected' : ''}>Offline</option>
                    <option value="maintenance" ${query.status === 'maintenance' ? 'selected' : ''}>Hoolduses</option>
                  </select>
                </div>

                <div class="sm:col-span-2 lg:col-span-1">
                  <label for="search-filter" class="block text-sm font-medium text-gray-700 mb-1">Otsing</label>
                  <input type="text" id="search-filter" placeholder="Masina number või nimi..."
                         value="${query.search || ''}"
                         class="w-full border border-gray-300 rounded-md px-3 py-3 text-sm min-h-[44px]">
                </div>

                <div class="flex items-end">
                  <button onclick="clearFilters()" class="w-full bg-gray-500 text-white px-4 py-3 rounded hover:bg-gray-600 text-sm min-h-[44px] flex items-center justify-center">
                    <i class="fas fa-times mr-1"></i>Tühista
                  </button>
                </div>
              </div>
            </div>

          <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Masina number
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nimi
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Grupp
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Asukoht
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Staatus
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Toimingud
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  ${machinesTableRows}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        </div>

        <script>
          // Mobile menu toggle
          function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            const icon = document.getElementById('mobile-menu-icon');

            if (menu.classList.contains('hidden')) {
              menu.classList.remove('hidden');
              icon.classList.remove('fa-bars');
              icon.classList.add('fa-times');
            } else {
              menu.classList.add('hidden');
              icon.classList.remove('fa-times');
              icon.classList.add('fa-bars');
            }
          }

          // Close mobile menu when clicking outside
          document.addEventListener('click', function(event) {
            const menu = document.getElementById('mobile-menu');
            const button = event.target.closest('button[onclick="toggleMobileMenu()"]');

            if (!button && !menu.contains(event.target) && !menu.classList.contains('hidden')) {
              toggleMobileMenu();
            }
          });

          // Filter functionality
          document.addEventListener('DOMContentLoaded', function() {
            const groupFilter = document.getElementById('group-filter');
            const statusFilter = document.getElementById('status-filter');
            const searchFilter = document.getElementById('search-filter');

            // Add event listeners
            groupFilter.addEventListener('change', applyFilters);
            statusFilter.addEventListener('change', applyFilters);
            searchFilter.addEventListener('input', debounce(applyFilters, 300));

            function applyFilters() {
              const params = new URLSearchParams();

              if (groupFilter.value) {
                params.set('group_id', groupFilter.value);
              }

              if (statusFilter.value) {
                params.set('status', statusFilter.value);
              }

              if (searchFilter.value.trim()) {
                params.set('search', searchFilter.value.trim());
              }

              // Redirect with filters
              const url = '/machines' + (params.toString() ? '?' + params.toString() : '');
              window.location.href = url;
            }

            function debounce(func, wait) {
              let timeout;
              return function executedFunction(...args) {
                const later = () => {
                  clearTimeout(timeout);
                  func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
              };
            }
          });

          function clearFilters() {
            window.location.href = '/machines';
          }

          async function regenerateAllQRCodes() {
            const button = event.target;
            const originalText = button.textContent;

            button.disabled = true;
            button.textContent = 'Uuendamine...';
            button.className = 'bg-gray-500 text-white px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center cursor-not-allowed';

            try {
              const response = await fetch('/api/machines/regenerate-all-qr', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                }
              });

              const result = await response.json();

              if (response.ok) {
                alert('QR koodid edukalt uuendatud: ' + result.message);
                // Refresh page to show updated QR codes
                window.location.reload();
              } else {
                alert('Viga QR koodide uuendamisel: ' + result.error);
              }
            } catch (error) {
              alert('Viga QR koodide uuendamisel: ' + error.message);
            } finally {
              button.disabled = false;
              button.textContent = originalText;
              button.className = 'bg-green-600 text-white px-4 py-3 rounded hover:bg-green-700 text-center min-h-[44px] flex items-center justify-center';
            }
          }
        </script>
      </body>
      </html>
    `;

    return new Response(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });
  } catch (error) {
    console.error('Error loading machines:', error);
    return c.text('Error loading machines', 500);
  }
});

// New machine form
pageRoutes.get('/machines/new', async (c) => {
  try {
    const groups = await MachineGroup.findAll({ is_active: true });

    return c.html(`
    <!DOCTYPE html>
    <html lang="et">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>CMMS - Lisa uus masin</title>
      <script src="https://cdn.tailwindcss.com"></script>
    </head>
    <body class="bg-gray-100">
      <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">Lisa uus masin</h1>

        <div class="bg-white rounded-lg shadow-md p-6 max-w-2xl">
          <form action="/machines" method="POST" class="space-y-6">
            <div>
              <label for="machine_number" class="block text-sm font-medium text-gray-700">
                Masina number *
              </label>
              <input type="text"
                     name="machine_number"
                     id="machine_number"
                     required
                     class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
            </div>

            <div>
              <label for="name" class="block text-sm font-medium text-gray-700">
                Nimi *
              </label>
              <input type="text"
                     name="name"
                     id="name"
                     required
                     class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="manufacturer" class="block text-sm font-medium text-gray-700">
                  Tootja
                </label>
                <input type="text"
                       name="manufacturer"
                       id="manufacturer"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>

              <div>
                <label for="model" class="block text-sm font-medium text-gray-700">
                  Mudel
                </label>
                <input type="text"
                       name="model"
                       id="model"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="department" class="block text-sm font-medium text-gray-700">
                  Osakond
                </label>
                <input type="text"
                       name="department"
                       id="department"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>

              <div>
                <label for="location" class="block text-sm font-medium text-gray-700">
                  Asukoht
                </label>
                <input type="text"
                       name="location"
                       id="location"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>
            </div>

            <div>
              <label for="group_id" class="block text-sm font-medium text-gray-700">
                Grupp
              </label>
              <select name="group_id" id="group_id"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                <option value="">Vali grupp...</option>
                ${groups.map(group => `
                  <option value="${group.id}">
                    ${group.name} (${group.machine_count} masinat)
                  </option>
                `).join('')}
              </select>
              <p class="text-xs text-gray-500 mt-1">Valikuline - masin saab hiljem gruppi määrata</p>
            </div>

            <div class="flex justify-end space-x-4">
              <a href="/admin"
                 class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                Tühista
              </a>
              <button type="submit"
                      class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                Lisa masin
              </button>
            </div>
          </form>
        </div>
      </div>
    </body>
    </html>
  `);
  } catch (error) {
    console.error('Error loading new machine form:', error);
    return c.text('Error loading form', 500);
  }
});

// Handle form submission for new machine
pageRoutes.post('/machines', async (c) => {
  try {
    const body = await c.req.parseBody();

    // Convert FormData to object
    const machineData = {};
    for (const [key, value] of Object.entries(body)) {
      machineData[key] = value;
    }

    const machine = await Machine.create(machineData);

    // Redirect to machine detail view with success message
    return c.redirect(`/machines/${machine.id}?success=created`);
  } catch (error) {
    console.error('Error creating machine:', error);

    // In a real app, we'd show the form again with error message
    return c.text(`Error creating machine: ${error.message}`, 500);
  }
});

// Operator view - mobile-friendly interface accessed via QR code
pageRoutes.get('/operator/:machineNumber', async (c) => {
  try {
    const machineNumber = c.req.param('machineNumber');
    const machine = await Machine.findByMachineNumber(machineNumber);

    if (!machine) {
      return c.html(html`
        <!DOCTYPE html>
        <html lang="et">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>CMMS - Masinat ei leitud</title>
          <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100 min-h-screen flex items-center justify-center">
          <div class="max-w-md w-full mx-4">
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
              <div class="alert-error bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                Masinat ei leitud
              </div>
              <div data-testid="error-message" class="text-gray-600 mb-4">
                Masina number "${machineNumber}" ei ole süsteemis registreeritud.
              </div>
              <a href="/admin" class="inline-block bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                Tagasi administraatori vaatesse
              </a>
            </div>
          </div>
        </body>
        </html>
      `, 404);
    }

    return c.html(html`
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - ${machine.name}</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
          /* Ensure mobile-friendly touch targets */
          button, input[type="submit"], input[type="button"] {
            min-height: 44px;
          }
        </style>
      </head>
      <body class="bg-gray-100 min-h-screen">
        <div class="container max-w-md mx-auto px-4 py-6">
          <!-- Machine Information -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div data-testid="machine-info" class="text-center mb-4">
              <h1 class="text-2xl font-bold text-gray-800 mb-2">
                <span data-testid="machine-name">${machine.name}</span>
              </h1>
              <p class="text-lg text-gray-600 mb-2">
                <span data-testid="machine-number">${machine.machine_number}</span>
              </p>
              <div class="flex justify-center items-center space-x-4 text-sm">
                <span data-testid="machine-status" class="inline-flex px-3 py-1 rounded-full text-xs font-semibold ${
                  machine.status === 'online' ? 'bg-green-100 text-green-800' :
                  machine.status === 'offline' ? 'bg-red-100 text-red-800' :
                  'bg-yellow-100 text-yellow-800'
                }">
                  ${machine.status}
                </span>
                ${machine.location ? html`
                  <span data-testid="machine-location" class="text-gray-500">
                    📍 ${machine.location}
                  </span>
                ` : ''}
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="space-y-4">
            <a href="/operator/${machine.machine_number}/issue"
               data-testid="report-issue-btn"
               class="block w-full bg-red-500 hover:bg-red-600 text-white text-center py-4 px-6 rounded-lg font-semibold text-lg">
              🚨 Teata rikest
            </a>

            <a href="/operator/${machine.machine_number}/maintenance"
               data-testid="report-maintenance-btn"
               class="block w-full bg-orange-500 hover:bg-orange-600 text-white text-center py-4 px-6 rounded-lg font-semibold text-lg">
              🔧 Teata hoolduse vajadusest
            </a>
          </div>

          <!-- Operator Number Input (for future issue reporting) -->
          <div class="mt-8 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold mb-4">Operaatori andmed</h2>
            <form class="space-y-4">
              <div>
                <label for="operator_number" class="block text-sm font-medium text-gray-700 mb-2">
                  Operaatori number
                </label>
                <input type="text"
                       name="operator_number"
                       id="operator_number"
                       placeholder="OP-123"
                       class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-lg"
                       autofocus>
              </div>
            </form>
          </div>

          <!-- Footer -->
          <div class="mt-8 text-center text-sm text-gray-500">
            <p>CMMS - Computerized Maintenance Management System</p>
          </div>
        </div>

        <script>
          // Progressive enhancement: Auto-focus first input
          document.addEventListener('DOMContentLoaded', function() {
            const operatorInput = document.querySelector('[name="operator_number"]');
            if (operatorInput) {
              operatorInput.focus();
            }
          });
        </script>
      </body>
      </html>
    `);
  } catch (error) {
    console.error('Error loading operator view:', error);
    return c.text('Error loading operator view', 500);
  }
});

// Machine detail view
pageRoutes.get('/machines/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const machine = await Machine.findById(id);

    if (!machine) {
      return c.text('Machine not found', 404);
    }

    const success = c.req.query('success');
    const qrCodeDataURL = await generateQRCodeDataURL(machine.machine_number);

    return c.html(`
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - ${machine.name}</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          ${success === 'created' ?
            '<div class="alert-success bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">' +
              'Masin edukalt lisatud' +
            '</div>'
          : success === 'updated' ?
            '<div class="alert-success bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">' +
              'Andmed edukalt uuendatud' +
            '</div>'
          : ''}

          <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">
              <span data-testid="machine-name">${machine.name}</span>
              <span class="text-gray-500 text-lg">
                (<span data-testid="machine-number">${machine.machine_number}</span>)
              </span>
            </h1>
            <div class="space-x-2">
              <a href="/machines/${machine.id}/edit"
                 data-testid="edit-machine"
                 class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded inline-block">
                Muuda andmeid
              </a>
              <button onclick="deleteMachine(${machine.id}, '${machine.machine_number}')"
                      data-testid="delete-machine"
                      class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
                Kustuta masin
              </button>
            </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Machine details -->
            <div class="lg:col-span-2">
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Masina andmed</h2>
                <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Tootja</dt>
                    <dd class="mt-1 text-sm text-gray-900">${machine.manufacturer || '-'}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Mudel</dt>
                    <dd class="mt-1 text-sm text-gray-900">${machine.model || '-'}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Osakond</dt>
                    <dd class="mt-1 text-sm text-gray-900">${machine.department || '-'}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Asukoht</dt>
                    <dd class="mt-1 text-sm text-gray-900">${machine.location || '-'}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Grupp</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                      ${machine.group_name ?
                        '<div class="flex items-center">' +
                          '<div class="w-3 h-3 rounded-full mr-2" style="background-color: ' + machine.group_color + '"></div>' +
                          '<span>' + machine.group_name + '</span>' +
                        '</div>'
                        : '-'}
                    </dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Staatus</dt>
                    <dd class="mt-1">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        machine.status === 'online' ? 'bg-green-100 text-green-800' :
                        machine.status === 'offline' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }">
                        ${machine.status}
                      </span>
                    </dd>
                  </div>
                </dl>
              </div>
            </div>

            <!-- QR Code -->
            <div class="lg:col-span-1">
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">QR-kood</h2>
                <div class="text-center">
                  <img src="${qrCodeDataURL}"
                       alt="QR kood masinale ${machine.machine_number}"
                       data-testid="qr-code"
                       class="mx-auto mb-4 border rounded">
                  <p class="text-sm text-gray-600 mb-4">
                    Skaneeri QR-kood operaatori vaate avamiseks
                  </p>
                  <a href="/api/files/qr/${machine.id}"
                     download="machine_${machine.machine_number}_qr.png"
                     data-testid="download-qr"
                     class="inline-block bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded text-sm">
                    Laadi alla PNG
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <script>
          async function deleteMachine(machineId, machineNumber) {
            // Double confirmation for safety
            const firstConfirm = confirm(
              \`Kas olete kindel, et soovite kustutada masina "\${machineNumber}"?\\n\\n\` +
              'See tegevus on pöördumatu ja kustutab ka kõik seotud andmed (QR kood, hooldusajalugu jne).'
            );

            if (!firstConfirm) {
              return;
            }

            const secondConfirm = confirm(
              \`VIIMANE HOIATUS!\\n\\n\` +
              \`Masin "\${machineNumber}" kustutatakse jäädavalt.\\n\\n\` +
              'Kas olete täiesti kindel?'
            );

            if (!secondConfirm) {
              return;
            }

            try {
              const response = await fetch(\`/api/machines/\${machineId}\`, {
                method: 'DELETE',
                headers: {
                  'Content-Type': 'application/json'
                }
              });

              const result = await response.json();

              if (response.ok) {
                alert(\`Masin "\${machineNumber}" edukalt kustutatud.\`);
                // Redirect to admin dashboard
                window.location.href = '/admin';
              } else {
                alert('Viga masina kustutamisel: ' + result.error);
              }
            } catch (error) {
              alert('Viga masina kustutamisel: ' + error.message);
            }
          }
        </script>
      </body>
      </html>
    `);
  } catch (error) {
    console.error('Error loading machine:', error);
    return c.text('Error loading machine', 500);
  }
});

// Machine edit form
pageRoutes.get('/machines/:id/edit', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const machine = await Machine.findById(id);
    const groups = await MachineGroup.findAll({ is_active: true });

    if (!machine) {
      return c.text('Machine not found', 404);
    }

    return c.html(`
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Muuda masinat: ${machine.name}</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          <h1 class="text-3xl font-bold text-gray-800 mb-8">Muuda masinat: ${machine.name}</h1>

          <div class="bg-white rounded-lg shadow-md p-6 max-w-2xl">
            <form action="/machines/${machine.id}" method="POST" class="space-y-6">
              <input type="hidden" name="_method" value="PUT">

              <div>
                <label for="machine_number" class="block text-sm font-medium text-gray-700">
                  Masina number *
                </label>
                <input type="text"
                       name="machine_number"
                       id="machine_number"
                       value="${machine.machine_number}"
                       required
                       readonly
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500">
                <p class="text-xs text-gray-500 mt-1">Masina numbrit ei saa muuta</p>
              </div>

              <div>
                <label for="name" class="block text-sm font-medium text-gray-700">
                  Nimi *
                </label>
                <input type="text"
                       name="name"
                       id="name"
                       value="${machine.name}"
                       required
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="manufacturer" class="block text-sm font-medium text-gray-700">
                    Tootja
                  </label>
                  <input type="text"
                         name="manufacturer"
                         id="manufacturer"
                         value="${machine.manufacturer || ''}"
                         class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                  <label for="model" class="block text-sm font-medium text-gray-700">
                    Mudel
                  </label>
                  <input type="text"
                         name="model"
                         id="model"
                         value="${machine.model || ''}"
                         class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="department" class="block text-sm font-medium text-gray-700">
                    Osakond
                  </label>
                  <input type="text"
                         name="department"
                         id="department"
                         value="${machine.department || ''}"
                         class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                  <label for="location" class="block text-sm font-medium text-gray-700">
                    Asukoht
                  </label>
                  <input type="text"
                         name="location"
                         id="location"
                         value="${machine.location || ''}"
                         class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="status" class="block text-sm font-medium text-gray-700">
                    Staatus
                  </label>
                  <select name="status"
                          id="status"
                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    <option value="online" ${machine.status === 'online' ? 'selected' : ''}>Online</option>
                    <option value="offline" ${machine.status === 'offline' ? 'selected' : ''}>Offline</option>
                    <option value="maintenance" ${machine.status === 'maintenance' ? 'selected' : ''}>Hoolduses</option>
                  </select>
                </div>

                <div>
                  <label for="group_id" class="block text-sm font-medium text-gray-700">
                    Grupp
                  </label>
                  <select name="group_id" id="group_id"
                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Vali grupp...</option>
                    ${groups.map(group => `
                      <option value="${group.id}" ${machine.group_id == group.id ? 'selected' : ''}>
                        ${group.name} (${group.machine_count} masinat)
                      </option>
                    `).join('')}
                  </select>
                </div>
              </div>

              <div class="flex justify-end space-x-4">
                <a href="/machines/${machine.id}"
                   class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                  Tühista
                </a>
                <button type="submit"
                        class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                  Salvesta muudatused
                </button>
              </div>
            </form>
          </div>
        </div>
      </body>
      </html>
    `);
  } catch (error) {
    console.error('Error loading machine edit form:', error);
    return c.text('Error loading edit form', 500);
  }
});

// Handle machine update
pageRoutes.post('/machines/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const body = await c.req.parseBody();

    // Convert FormData to object, excluding _method
    const updateData = {};
    for (const [key, value] of Object.entries(body)) {
      if (key !== '_method') {
        updateData[key] = value;
      }
    }

    const machine = await Machine.update(id, updateData);

    if (!machine) {
      return c.text('Machine not found', 404);
    }

    // Redirect to machine detail view with success message
    return c.redirect(`/machines/${machine.id}?success=updated`);
  } catch (error) {
    console.error('Error updating machine:', error);
    return c.text(`Error updating machine: ${error.message}`, 500);
  }
});

// Handle machine deletion (DELETE request from frontend)
pageRoutes.delete('/machines/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const deleted = await Machine.delete(id);

    if (!deleted) {
      return c.json({ error: 'Machine not found' }, 404);
    }

    return c.json({ message: 'Machine deleted successfully' });
  } catch (error) {
    console.error('Error deleting machine:', error);
    return c.json({ error: 'Failed to delete machine' }, 500);
  }
});

// Issue reporting form for operators
pageRoutes.get('/operator/:machineNumber/issue', async (c) => {
  try {
    const machineNumber = c.req.param('machineNumber');
    const machine = await Machine.findByMachineNumber(machineNumber);

    if (!machine) {
      return c.html(`
        <!DOCTYPE html>
        <html lang="et">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>CMMS - Masinat ei leitud</title>
          <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100 min-h-screen flex items-center justify-center">
          <div class="max-w-md w-full mx-4">
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
              <div class="alert-error bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                Masinat ei leitud
              </div>
              <div class="text-gray-600 mb-4">
                Masina number "${machineNumber}" ei ole süsteemis registreeritud.
              </div>
              <a href="/admin" class="inline-block bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                Tagasi administraatori vaatesse
              </a>
            </div>
          </div>
        </body>
        </html>
      `, 404);
    }

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Teata rikest: ${machine.name}</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
          /* Ensure mobile-friendly touch targets */
          button, input[type="submit"], input[type="button"], select {
            min-height: 44px;
          }
        </style>
      </head>
      <body class="bg-gray-100 min-h-screen">
        <div class="container max-w-2xl mx-auto px-4 py-6">
          <!-- Header -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">🚨 Teata rikest</h1>
            <div class="text-gray-600">
              <p><strong>Masin:</strong> ${machine.name}</p>
              <p><strong>Number:</strong> ${machine.machine_number}</p>
              <p><strong>Asukoht:</strong> ${machine.location || 'Määramata'}</p>
            </div>
          </div>

          <!-- Issue Form -->
          <div class="bg-white rounded-lg shadow-md p-6">
            <form data-testid="issue-form" id="issueForm" class="space-y-6">
              <input type="hidden" name="machine_id" value="${machine.id}">

              <!-- Operator Information -->
              <div class="border-b pb-4">
                <h2 class="text-lg font-semibold mb-4">Operaatori andmed</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label for="operator_number" class="block text-sm font-medium text-gray-700 mb-2">
                      Operaatori number *
                    </label>
                    <input type="text"
                           name="operator_number"
                           id="operator_number"
                           required
                           placeholder="OP-123"
                           class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                  </div>
                  <div>
                    <label for="operator_name" class="block text-sm font-medium text-gray-700 mb-2">
                      Operaatori nimi
                    </label>
                    <input type="text"
                           name="operator_name"
                           id="operator_name"
                           placeholder="Nimi Perekonnanimi"
                           class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                  </div>
                </div>
              </div>

              <!-- Issue Details -->
              <div>
                <h2 class="text-lg font-semibold mb-4">Rikke andmed</h2>
                <div class="space-y-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label for="issue_type" class="block text-sm font-medium text-gray-700 mb-2">
                        Rikke tüüp *
                      </label>
                      <select name="issue_type"
                              id="issue_type"
                              required
                              class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                        <option value="">Vali rikke tüüp</option>
                        <option value="mechanical">🔧 Mehaaniline</option>
                        <option value="electrical">⚡ Elektriline</option>
                        <option value="software">💻 Tarkvara</option>
                        <option value="safety">⚠️ Ohutus</option>
                        <option value="quality">✅ Kvaliteet</option>
                        <option value="other">❓ Muu</option>
                      </select>
                    </div>
                    <div>
                      <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
                        Prioriteet *
                      </label>
                      <select name="priority"
                              id="priority"
                              required
                              class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                        <option value="">Vali prioriteet</option>
                        <option value="low">🟢 Madal</option>
                        <option value="medium" selected>🟡 Keskmine</option>
                        <option value="high">🟠 Kõrge</option>
                        <option value="critical">🔴 Kriitiline</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                      Rikke pealkiri *
                    </label>
                    <input type="text"
                           name="title"
                           id="title"
                           required
                           placeholder="Lühike rikke kirjeldus"
                           maxlength="200"
                           class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                  </div>

                  <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                      Detailne kirjeldus
                    </label>
                    <textarea name="description"
                              id="description"
                              rows="4"
                              placeholder="Kirjelda rikke sümptomeid, millal see tekkis, mis tingimuste all jne..."
                              class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500"></textarea>
                  </div>
                </div>
              </div>

              <!-- Error/Success Messages -->
              <div id="messageContainer" class="hidden">
                <div id="errorMessage" class="error-message bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 hidden"></div>
                <div id="successMessage" class="success-message bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 hidden"></div>
              </div>

              <!-- Submit Button -->
              <div class="flex flex-col sm:flex-row gap-4">
                <button type="submit"
                        data-testid="submit-issue"
                        class="flex-1 bg-red-500 hover:bg-red-600 text-white py-4 px-6 rounded-lg font-semibold text-lg">
                  🚨 Saada rike teatis
                </button>
                <a href="/operator/${machine.machine_number}"
                   class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-4 px-6 rounded-lg font-semibold text-lg text-center">
                  ← Tagasi
                </a>
              </div>
            </form>
          </div>
        </div>

        <script>
          document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('issueForm');
            const messageContainer = document.getElementById('messageContainer');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');

            // Auto-focus first input
            const operatorNumberInput = document.querySelector('[name="operator_number"]');
            if (operatorNumberInput) {
              operatorNumberInput.focus();
            }

            form.addEventListener('submit', async function(e) {
              e.preventDefault();

              // Hide previous messages
              messageContainer.classList.add('hidden');
              errorMessage.classList.add('hidden');
              successMessage.classList.add('hidden');

              // Get form data
              const formData = new FormData(form);
              const issueData = Object.fromEntries(formData.entries());

              // Basic validation
              if (!issueData.operator_number || !issueData.issue_type || !issueData.priority || !issueData.title) {
                showError('Palun täida kõik kohustuslikud väljad (*)');
                return;
              }

              try {
                const response = await fetch('/api/issues', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify(issueData)
                });

                const result = await response.json();

                if (response.ok) {
                  showSuccess('Rike teatis edukalt saadetud! Hooldusmeeskond on teavitatud.');
                  form.reset();
                  // Redirect after 2 seconds
                  setTimeout(() => {
                    window.location.href = '/operator/${machine.machine_number}';
                  }, 2000);
                } else {
                  showError('Viga rike teatamisel: ' + (result.error || 'Tundmatu viga'));
                }
              } catch (error) {
                showError('Viga rike teatamisel: ' + error.message);
              }
            });

            function showError(message) {
              errorMessage.textContent = message;
              errorMessage.classList.remove('hidden');
              messageContainer.classList.remove('hidden');
              window.scrollTo({ top: 0, behavior: 'smooth' });
            }

            function showSuccess(message) {
              successMessage.textContent = message;
              successMessage.classList.remove('hidden');
              messageContainer.classList.remove('hidden');
              window.scrollTo({ top: 0, behavior: 'smooth' });
            }
          });
        </script>
      </body>
      </html>
    `;

    return new Response(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });
  } catch (error) {
    console.error('Error loading issue form:', error);
    return c.text('Error loading issue form', 500);
  }
});

// Maintenance reporting form for operators
pageRoutes.get('/operator/:machineNumber/maintenance', async (c) => {
  try {
    const machineNumber = c.req.param('machineNumber');
    const machine = await Machine.findByMachineNumber(machineNumber);

    if (!machine) {
      return c.html(`
        <!DOCTYPE html>
        <html lang="et">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>CMMS - Masinat ei leitud</title>
          <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100 min-h-screen flex items-center justify-center">
          <div class="max-w-md w-full mx-4">
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
              <div class="alert-error bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                Masinat ei leitud
              </div>
              <div class="text-gray-600 mb-4">
                Masina number "${machineNumber}" ei ole süsteemis registreeritud.
              </div>
              <a href="/admin" class="inline-block bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                Tagasi administraatori vaatesse
              </a>
            </div>
          </div>
        </body>
        </html>
      `, 404);
    }

    // Get today's date for default requested_date
    const today = new Date().toISOString().split('T')[0];

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Teata hoolduse vajadusest: ${machine.name}</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
          /* Ensure mobile-friendly touch targets */
          button, input[type="submit"], input[type="button"], select {
            min-height: 44px;
          }
        </style>
      </head>
      <body class="bg-gray-100 min-h-screen">
        <div class="container max-w-2xl mx-auto px-4 py-6">
          <!-- Header -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">🔧 Teata hoolduse vajadusest</h1>
            <div class="text-gray-600">
              <p><strong>Masin:</strong> ${machine.name}</p>
              <p><strong>Number:</strong> ${machine.machine_number}</p>
              <p><strong>Asukoht:</strong> ${machine.location || 'Määramata'}</p>
            </div>
          </div>

          <!-- Maintenance Form -->
          <div class="bg-white rounded-lg shadow-md p-6">
            <form data-testid="maintenance-form" id="maintenanceForm" class="space-y-6">
              <input type="hidden" name="machine_id" value="${machine.id}">

              <!-- Operator Information -->
              <div class="border-b pb-4">
                <h2 class="text-lg font-semibold mb-4">Operaatori andmed</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label for="operator_number" class="block text-sm font-medium text-gray-700 mb-2">
                      Operaatori number *
                    </label>
                    <input type="text"
                           name="operator_number"
                           id="operator_number"
                           required
                           placeholder="OP-123"
                           class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                  </div>
                  <div>
                    <label for="operator_name" class="block text-sm font-medium text-gray-700 mb-2">
                      Operaatori nimi
                    </label>
                    <input type="text"
                           name="operator_name"
                           id="operator_name"
                           placeholder="Nimi Perekonnanimi"
                           class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                  </div>
                </div>
              </div>

              <!-- Maintenance Details -->
              <div>
                <h2 class="text-lg font-semibold mb-4">Hoolduse andmed</h2>
                <div class="space-y-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label for="maintenance_type" class="block text-sm font-medium text-gray-700 mb-2">
                        Hoolduse tüüp *
                      </label>
                      <select name="maintenance_type"
                              id="maintenance_type"
                              required
                              class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                        <option value="">Vali hoolduse tüüp</option>
                        <option value="preventive">🔧 Ennetav hooldus</option>
                        <option value="corrective">🛠️ Parandav hooldus</option>
                        <option value="emergency">🚨 Hädahooldus</option>
                        <option value="inspection">🔍 Kontroll/Inspektsioon</option>
                        <option value="calibration">⚖️ Kalibreerimine</option>
                        <option value="cleaning">🧽 Puhastamine</option>
                        <option value="other">❓ Muu</option>
                      </select>
                    </div>
                    <div>
                      <label for="urgency" class="block text-sm font-medium text-gray-700 mb-2">
                        Kiireloomulisus *
                      </label>
                      <select name="urgency"
                              id="urgency"
                              required
                              class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                        <option value="">Vali kiireloomulisus</option>
                        <option value="low">🟢 Madal</option>
                        <option value="medium" selected>🟡 Keskmine</option>
                        <option value="high">🟠 Kõrge</option>
                        <option value="urgent">🔴 Kiire</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                      Hoolduse pealkiri *
                    </label>
                    <input type="text"
                           name="title"
                           id="title"
                           required
                           placeholder="Lühike hoolduse kirjeldus"
                           maxlength="200"
                           class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                  </div>

                  <div>
                    <label for="requested_date" class="block text-sm font-medium text-gray-700 mb-2">
                      Soovitud kuupäev
                    </label>
                    <input type="date"
                           name="requested_date"
                           id="requested_date"
                           value="${today}"
                           class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                  </div>

                  <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                      Detailne kirjeldus
                    </label>
                    <textarea name="description"
                              id="description"
                              rows="4"
                              placeholder="Kirjelda hoolduse vajadust, märgatud probleeme, viimast hooldust jne..."
                              class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500"></textarea>
                  </div>
                </div>
              </div>

              <!-- Error/Success Messages -->
              <div id="messageContainer" class="hidden">
                <div id="errorMessage" class="error-message bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 hidden"></div>
                <div id="successMessage" class="success-message bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 hidden"></div>
              </div>

              <!-- Submit Button -->
              <div class="flex flex-col sm:flex-row gap-4">
                <button type="submit"
                        data-testid="submit-maintenance"
                        class="flex-1 bg-orange-500 hover:bg-orange-600 text-white py-4 px-6 rounded-lg font-semibold text-lg">
                  🔧 Saada hoolduse taotlus
                </button>
                <a href="/operator/${machine.machine_number}"
                   class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-4 px-6 rounded-lg font-semibold text-lg text-center">
                  ← Tagasi
                </a>
              </div>
            </form>
          </div>
        </div>

        <script>
          document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('maintenanceForm');
            const messageContainer = document.getElementById('messageContainer');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');

            // Auto-focus first input
            const operatorNumberInput = document.querySelector('[name="operator_number"]');
            if (operatorNumberInput) {
              operatorNumberInput.focus();
            }

            form.addEventListener('submit', async function(e) {
              e.preventDefault();

              // Hide previous messages
              messageContainer.classList.add('hidden');
              errorMessage.classList.add('hidden');
              successMessage.classList.add('hidden');

              // Get form data
              const formData = new FormData(form);
              const maintenanceData = Object.fromEntries(formData.entries());

              // Basic validation
              if (!maintenanceData.operator_number || !maintenanceData.maintenance_type || !maintenanceData.urgency || !maintenanceData.title) {
                showError('Palun täida kõik kohustuslikud väljad (*)');
                return;
              }

              try {
                const response = await fetch('/api/maintenance', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify(maintenanceData)
                });

                const result = await response.json();

                if (response.ok) {
                  showSuccess('Hoolduse taotlus edukalt saadetud! Hooldusmeeskond on teavitatud.');
                  form.reset();
                  // Reset date to today
                  document.getElementById('requested_date').value = '${today}';
                  // Redirect after 2 seconds
                  setTimeout(() => {
                    window.location.href = '/operator/${machine.machine_number}';
                  }, 2000);
                } else {
                  showError('Viga hoolduse taotlemisel: ' + (result.error || 'Tundmatu viga'));
                }
              } catch (error) {
                showError('Viga hoolduse taotlemisel: ' + error.message);
              }
            });

            function showError(message) {
              errorMessage.textContent = message;
              errorMessage.classList.remove('hidden');
              messageContainer.classList.remove('hidden');
              window.scrollTo({ top: 0, behavior: 'smooth' });
            }

            function showSuccess(message) {
              successMessage.textContent = message;
              successMessage.classList.remove('hidden');
              messageContainer.classList.remove('hidden');
              window.scrollTo({ top: 0, behavior: 'smooth' });
            }
          });
        </script>
      </body>
      </html>
    `;

    return new Response(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });
  } catch (error) {
    console.error('Error loading maintenance form:', error);
    return c.text('Error loading maintenance form', 500);
  }
});

// Issues management page for administrators
pageRoutes.get('/admin/issues', async (c) => {
  try {
    const query = c.req.query();
    const filters = {};

    // Parse query parameters for filtering
    if (query.status) filters.status = query.status;
    if (query.priority) filters.priority = query.priority;
    if (query.machine_id) filters.machine_id = parseInt(query.machine_id);

    const issues = await Issue.findAll(filters);
    const machines = await Machine.findAll();
    const issueStats = await Issue.getStatistics();

    const issuesTableRows = issues.map(issue => `
      <tr class="hover:bg-gray-50">
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
          #${issue.id}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          ${issue.machine_number}
        </td>
        <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
          ${issue.title}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${issue.operator_number}
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            issue.priority === 'critical' ? 'bg-red-100 text-red-800' :
            issue.priority === 'high' ? 'bg-orange-100 text-orange-800' :
            issue.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
            'bg-green-100 text-green-800'
          }">
            ${issue.priority}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            issue.status === 'open' ? 'bg-red-100 text-red-800' :
            issue.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' :
            issue.status === 'resolved' ? 'bg-green-100 text-green-800' :
            'bg-gray-100 text-gray-800'
          }">
            ${issue.status}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${new Date(issue.reported_at).toLocaleDateString('et-EE')}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <a href="/admin/issues/${issue.id}" class="text-blue-600 hover:text-blue-900 mr-2">
            Vaata
          </a>
          <button onclick="updateIssueStatus(${issue.id}, '${issue.status}')"
                  class="text-green-600 hover:text-green-900">
            Muuda
          </button>
        </td>
      </tr>
    `).join('');

    const machineOptions = machines.map(machine => `
      <option value="${machine.id}" ${filters.machine_id === machine.id ? 'selected' : ''}>
        ${machine.machine_number} - ${machine.name}
      </option>
    `).join('');

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Rikete haldamine</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
      </head>
      <body class="bg-gray-50">
        <div class="min-h-screen">
          <!-- Navigation -->
          <nav class="bg-blue-600 text-white">
            <div class="container mx-auto px-4">
              <!-- Desktop Navigation -->
              <div class="hidden md:flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                  <h1 class="text-xl font-bold">CMMS - Rikked</h1>
                </div>
                <div class="flex space-x-4">
                  <a href="/admin" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                  </a>
                  <a href="/machines" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-cogs mr-2"></i>Masinad
                  </a>
                  <a href="/admin/machine-groups" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-layer-group mr-2"></i>Grupid
                  </a>
                  <a href="/admin/reports" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-chart-bar mr-2"></i>Aruanded
                  </a>
                  <a href="/admin/issues" class="bg-blue-800 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Rikked
                  </a>
                  <a href="/admin/maintenance" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-wrench mr-2"></i>Hooldus
                  </a>
                  <a href="/admin/partners" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-handshake mr-2"></i>Partnerid
                  </a>
                </div>
              </div>

              <!-- Mobile Navigation -->
              <div class="md:hidden">
                <div class="flex justify-between items-center py-3">
                  <h1 class="text-lg font-bold">CMMS - Rikked</h1>
                  <button onclick="toggleMobileMenu()" class="p-2 rounded hover:bg-blue-700 min-h-[44px] min-w-[44px]">
                    <i id="mobile-menu-icon" class="fas fa-bars text-xl"></i>
                  </button>
                </div>

                <!-- Mobile Menu -->
                <div id="mobile-menu" class="hidden pb-4">
                  <div class="space-y-2">
                    <a href="/admin" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-home mr-2"></i>Dashboard
                    </a>
                    <a href="/machines" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-cogs mr-2"></i>Masinad
                    </a>
                    <a href="/admin/machine-groups" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-layer-group mr-2"></i>Grupid
                    </a>
                    <a href="/admin/reports" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-chart-bar mr-2"></i>Aruanded
                    </a>
                    <a href="/admin/issues" class="block bg-blue-800 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-exclamation-triangle mr-2"></i>Rikked
                    </a>
                    <a href="/admin/maintenance" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-wrench mr-2"></i>Hooldus
                    </a>
                    <a href="/admin/partners" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-handshake mr-2"></i>Partnerid
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </nav>

          <div class="container mx-auto px-4 py-8">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-2xl font-bold text-gray-800">Rikete haldamine</h2>
            </div>

          <!-- Statistics -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                  <i class="fas fa-clipboard-list text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Kokku rikked</p>
                  <p class="text-2xl font-semibold text-gray-900">${issueStats.total_issues || 0}</p>
                </div>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">🚨</div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Avatud</p>
                  <p class="text-2xl font-semibold text-gray-900">${issueStats.open_issues || 0}</p>
                </div>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">🔧</div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Töös</p>
                  <p class="text-2xl font-semibold text-gray-900">${issueStats.in_progress_issues || 0}</p>
                </div>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100 text-orange-600">⚠️</div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Kriitilised</p>
                  <p class="text-2xl font-semibold text-gray-900">${issueStats.critical_issues || 0}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Filters -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold mb-4">Filtrid</h2>
            <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Staatus</label>
                <select name="status" id="status" class="w-full border-gray-300 rounded-md shadow-sm">
                  <option value="">Kõik staatused</option>
                  <option value="open" ${filters.status === 'open' ? 'selected' : ''}>Avatud</option>
                  <option value="in_progress" ${filters.status === 'in_progress' ? 'selected' : ''}>Töös</option>
                  <option value="resolved" ${filters.status === 'resolved' ? 'selected' : ''}>Lahendatud</option>
                  <option value="closed" ${filters.status === 'closed' ? 'selected' : ''}>Suletud</option>
                </select>
              </div>
              <div>
                <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">Prioriteet</label>
                <select name="priority" id="priority" class="w-full border-gray-300 rounded-md shadow-sm">
                  <option value="">Kõik prioriteedid</option>
                  <option value="low" ${filters.priority === 'low' ? 'selected' : ''}>Madal</option>
                  <option value="medium" ${filters.priority === 'medium' ? 'selected' : ''}>Keskmine</option>
                  <option value="high" ${filters.priority === 'high' ? 'selected' : ''}>Kõrge</option>
                  <option value="critical" ${filters.priority === 'critical' ? 'selected' : ''}>Kriitiline</option>
                </select>
              </div>
              <div>
                <label for="machine_id" class="block text-sm font-medium text-gray-700 mb-2">Masin</label>
                <select name="machine_id" id="machine_id" class="w-full border-gray-300 rounded-md shadow-sm">
                  <option value="">Kõik masinad</option>
                  ${machineOptions}
                </select>
              </div>
              <div class="flex items-end">
                <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                  Filtreeri
                </button>
              </div>
            </form>
          </div>

          <!-- Issues Table -->
          <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-semibold">Rikked (${issues.length})</h2>
            </div>
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Masin</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pealkiri</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Operaator</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prioriteet</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Staatus</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kuupäev</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toimingud</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  ${issuesTableRows || '<tr><td colspan="8" class="px-6 py-4 text-center text-gray-500">Rikked puuduvad</td></tr>'}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <script>
          // Mobile menu toggle
          function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            const icon = document.getElementById('mobile-menu-icon');

            if (menu.classList.contains('hidden')) {
              menu.classList.remove('hidden');
              icon.classList.remove('fa-bars');
              icon.classList.add('fa-times');
            } else {
              menu.classList.add('hidden');
              icon.classList.remove('fa-times');
              icon.classList.add('fa-bars');
            }
          }

          // Close mobile menu when clicking outside
          document.addEventListener('click', function(event) {
            const menu = document.getElementById('mobile-menu');
            const button = event.target.closest('button[onclick="toggleMobileMenu()"]');

            if (!button && !menu.contains(event.target) && !menu.classList.contains('hidden')) {
              toggleMobileMenu();
            }
          });

          async function updateIssueStatus(issueId, currentStatus) {
            const statuses = ['open', 'in_progress', 'resolved', 'closed'];
            const statusLabels = {
              'open': 'Avatud',
              'in_progress': 'Töös',
              'resolved': 'Lahendatud',
              'closed': 'Suletud'
            };

            const newStatus = prompt(
              'Vali uus staatus:\\n' +
              statuses.map((s, i) => \`\${i + 1}. \${statusLabels[s]}\`).join('\\n'),
              statuses.indexOf(currentStatus) + 1
            );

            if (newStatus && newStatus >= 1 && newStatus <= 4) {
              const selectedStatus = statuses[newStatus - 1];

              try {
                const response = await fetch(\`/api/issues/\${issueId}\`, {
                  method: 'PUT',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ status: selectedStatus })
                });

                if (response.ok) {
                  alert('Staatus edukalt uuendatud!');
                  window.location.reload();
                } else {
                  alert('Viga staatuse uuendamisel!');
                }
              } catch (error) {
                alert('Viga: ' + error.message);
              }
            }
          }
        </script>
      </body>
      </html>
    `;

    return new Response(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });
  } catch (error) {
    console.error('Error loading issues management:', error);
    return c.text('Error loading issues management', 500);
  }
});

// Maintenance management page for administrators
pageRoutes.get('/admin/maintenance', async (c) => {
  try {
    const query = c.req.query();
    const filters = {};

    // Parse query parameters for filtering
    if (query.status) filters.status = query.status;
    if (query.urgency) filters.urgency = query.urgency;
    if (query.maintenance_type) filters.maintenance_type = query.maintenance_type;
    if (query.machine_id) filters.machine_id = parseInt(query.machine_id);

    const maintenanceRequests = await MaintenanceRequest.findAll(filters);
    const machines = await Machine.findAll();
    const maintenanceStats = await MaintenanceRequest.getStatistics();

    const maintenanceTableRows = maintenanceRequests.map(request => `
      <tr class="hover:bg-gray-50">
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
          #${request.id}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          ${request.machine_number}
        </td>
        <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
          ${request.title}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${request.operator_number}
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            request.maintenance_type === 'emergency' ? 'bg-red-100 text-red-800' :
            request.maintenance_type === 'preventive' ? 'bg-green-100 text-green-800' :
            request.maintenance_type === 'corrective' ? 'bg-yellow-100 text-yellow-800' :
            'bg-blue-100 text-blue-800'
          }">
            ${request.maintenance_type}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            request.urgency === 'urgent' ? 'bg-red-100 text-red-800' :
            request.urgency === 'high' ? 'bg-orange-100 text-orange-800' :
            request.urgency === 'medium' ? 'bg-yellow-100 text-yellow-800' :
            'bg-green-100 text-green-800'
          }">
            ${request.urgency}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            request.status === 'requested' ? 'bg-yellow-100 text-yellow-800' :
            request.status === 'scheduled' ? 'bg-blue-100 text-blue-800' :
            request.status === 'in_progress' ? 'bg-orange-100 text-orange-800' :
            request.status === 'completed' ? 'bg-green-100 text-green-800' :
            'bg-gray-100 text-gray-800'
          }">
            ${request.status}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${request.requested_date ? new Date(request.requested_date).toLocaleDateString('et-EE') :
            new Date(request.created_at).toLocaleDateString('et-EE')}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <a href="/admin/maintenance/${request.id}" class="text-blue-600 hover:text-blue-900 mr-2">
            Vaata
          </a>
          <button onclick="updateMaintenanceStatus(${request.id}, '${request.status}')"
                  class="text-green-600 hover:text-green-900">
            Muuda
          </button>
        </td>
      </tr>
    `).join('');

    const machineOptions = machines.map(machine => `
      <option value="${machine.id}" ${filters.machine_id === machine.id ? 'selected' : ''}>
        ${machine.machine_number} - ${machine.name}
      </option>
    `).join('');

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Hoolduse haldamine</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
      </head>
      <body class="bg-gray-50">
        <div class="min-h-screen">
          <!-- Navigation -->
          <nav class="bg-blue-600 text-white">
            <div class="container mx-auto px-4">
              <!-- Desktop Navigation -->
              <div class="hidden md:flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                  <h1 class="text-xl font-bold">CMMS - Hooldus</h1>
                </div>
                <div class="flex space-x-4">
                  <a href="/admin" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                  </a>
                  <a href="/machines" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-cogs mr-2"></i>Masinad
                  </a>
                  <a href="/admin/machine-groups" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-layer-group mr-2"></i>Grupid
                  </a>
                  <a href="/admin/reports" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-chart-bar mr-2"></i>Aruanded
                  </a>
                  <a href="/admin/issues" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Rikked
                  </a>
                  <a href="/admin/maintenance" class="bg-blue-800 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-wrench mr-2"></i>Hooldus
                  </a>
                  <a href="/admin/partners" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-handshake mr-2"></i>Partnerid
                  </a>
                </div>
              </div>

              <!-- Mobile Navigation -->
              <div class="md:hidden">
                <div class="flex justify-between items-center py-3">
                  <h1 class="text-lg font-bold">CMMS - Hooldus</h1>
                  <button onclick="toggleMobileMenu()" class="p-2 rounded hover:bg-blue-700 min-h-[44px] min-w-[44px]">
                    <i id="mobile-menu-icon" class="fas fa-bars text-xl"></i>
                  </button>
                </div>

                <!-- Mobile Menu -->
                <div id="mobile-menu" class="hidden pb-4">
                  <div class="space-y-2">
                    <a href="/admin" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-home mr-2"></i>Dashboard
                    </a>
                    <a href="/machines" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-cogs mr-2"></i>Masinad
                    </a>
                    <a href="/admin/machine-groups" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-layer-group mr-2"></i>Grupid
                    </a>
                    <a href="/admin/reports" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-chart-bar mr-2"></i>Aruanded
                    </a>
                    <a href="/admin/issues" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-exclamation-triangle mr-2"></i>Rikked
                    </a>
                    <a href="/admin/maintenance" class="block bg-blue-800 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-wrench mr-2"></i>Hooldus
                    </a>
                    <a href="/admin/partners" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-handshake mr-2"></i>Partnerid
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </nav>

          <div class="container mx-auto px-4 py-8">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-2xl font-bold text-gray-800">Hoolduse haldamine</h2>
            </div>

          <!-- Statistics -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">🔧</div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Kokku taotlused</p>
                  <p class="text-2xl font-semibold text-gray-900">${maintenanceStats.total_requests || 0}</p>
                </div>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">⏳</div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Ootel</p>
                  <p class="text-2xl font-semibold text-gray-900">${maintenanceStats.pending_requests || 0}</p>
                </div>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">📅</div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Planeeritud</p>
                  <p class="text-2xl font-semibold text-gray-900">${maintenanceStats.scheduled_requests || 0}</p>
                </div>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">🚨</div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Kiireloomulised</p>
                  <p class="text-2xl font-semibold text-gray-900">${maintenanceStats.urgent_requests || 0}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Filters -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold mb-4">Filtrid</h2>
            <form method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Staatus</label>
                <select name="status" id="status" class="w-full border-gray-300 rounded-md shadow-sm">
                  <option value="">Kõik staatused</option>
                  <option value="requested" ${filters.status === 'requested' ? 'selected' : ''}>Taotletud</option>
                  <option value="scheduled" ${filters.status === 'scheduled' ? 'selected' : ''}>Planeeritud</option>
                  <option value="in_progress" ${filters.status === 'in_progress' ? 'selected' : ''}>Töös</option>
                  <option value="completed" ${filters.status === 'completed' ? 'selected' : ''}>Lõpetatud</option>
                  <option value="cancelled" ${filters.status === 'cancelled' ? 'selected' : ''}>Tühistatud</option>
                </select>
              </div>
              <div>
                <label for="urgency" class="block text-sm font-medium text-gray-700 mb-2">Kiireloomulisus</label>
                <select name="urgency" id="urgency" class="w-full border-gray-300 rounded-md shadow-sm">
                  <option value="">Kõik tasemed</option>
                  <option value="low" ${filters.urgency === 'low' ? 'selected' : ''}>Madal</option>
                  <option value="medium" ${filters.urgency === 'medium' ? 'selected' : ''}>Keskmine</option>
                  <option value="high" ${filters.urgency === 'high' ? 'selected' : ''}>Kõrge</option>
                  <option value="urgent" ${filters.urgency === 'urgent' ? 'selected' : ''}>Kiire</option>
                </select>
              </div>
              <div>
                <label for="maintenance_type" class="block text-sm font-medium text-gray-700 mb-2">Tüüp</label>
                <select name="maintenance_type" id="maintenance_type" class="w-full border-gray-300 rounded-md shadow-sm">
                  <option value="">Kõik tüübid</option>
                  <option value="preventive" ${filters.maintenance_type === 'preventive' ? 'selected' : ''}>Ennetav</option>
                  <option value="corrective" ${filters.maintenance_type === 'corrective' ? 'selected' : ''}>Parandav</option>
                  <option value="emergency" ${filters.maintenance_type === 'emergency' ? 'selected' : ''}>Hädahooldus</option>
                  <option value="inspection" ${filters.maintenance_type === 'inspection' ? 'selected' : ''}>Kontroll</option>
                  <option value="calibration" ${filters.maintenance_type === 'calibration' ? 'selected' : ''}>Kalibreerimine</option>
                  <option value="cleaning" ${filters.maintenance_type === 'cleaning' ? 'selected' : ''}>Puhastamine</option>
                  <option value="other" ${filters.maintenance_type === 'other' ? 'selected' : ''}>Muu</option>
                </select>
              </div>
              <div>
                <label for="machine_id" class="block text-sm font-medium text-gray-700 mb-2">Masin</label>
                <select name="machine_id" id="machine_id" class="w-full border-gray-300 rounded-md shadow-sm">
                  <option value="">Kõik masinad</option>
                  ${machineOptions}
                </select>
              </div>
              <div class="flex items-end">
                <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                  Filtreeri
                </button>
              </div>
            </form>
          </div>

          <!-- Maintenance Table -->
          <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-semibold">Hoolduse taotlused (${maintenanceRequests.length})</h2>
            </div>
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Masin</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pealkiri</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Operaator</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tüüp</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kiireloomulisus</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Staatus</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kuupäev</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toimingud</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  ${maintenanceTableRows || '<tr><td colspan="9" class="px-6 py-4 text-center text-gray-500">Hoolduse taotlused puuduvad</td></tr>'}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <script>
          // Mobile menu toggle
          function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            const icon = document.getElementById('mobile-menu-icon');

            if (menu.classList.contains('hidden')) {
              menu.classList.remove('hidden');
              icon.classList.remove('fa-bars');
              icon.classList.add('fa-times');
            } else {
              menu.classList.add('hidden');
              icon.classList.remove('fa-times');
              icon.classList.add('fa-bars');
            }
          }

          // Close mobile menu when clicking outside
          document.addEventListener('click', function(event) {
            const menu = document.getElementById('mobile-menu');
            const button = event.target.closest('button[onclick="toggleMobileMenu()"]');

            if (!button && !menu.contains(event.target) && !menu.classList.contains('hidden')) {
              toggleMobileMenu();
            }
          });

          async function updateMaintenanceStatus(requestId, currentStatus) {
            const statuses = ['requested', 'scheduled', 'in_progress', 'completed', 'cancelled'];
            const statusLabels = {
              'requested': 'Taotletud',
              'scheduled': 'Planeeritud',
              'in_progress': 'Töös',
              'completed': 'Lõpetatud',
              'cancelled': 'Tühistatud'
            };

            const newStatus = prompt(
              'Vali uus staatus:\\n' +
              statuses.map((s, i) => \`\${i + 1}. \${statusLabels[s]}\`).join('\\n'),
              statuses.indexOf(currentStatus) + 1
            );

            if (newStatus && newStatus >= 1 && newStatus <= 5) {
              const selectedStatus = statuses[newStatus - 1];

              try {
                const response = await fetch(\`/api/maintenance/\${requestId}\`, {
                  method: 'PUT',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ status: selectedStatus })
                });

                if (response.ok) {
                  alert('Staatus edukalt uuendatud!');
                  window.location.reload();
                } else {
                  alert('Viga staatuse uuendamisel!');
                }
              } catch (error) {
                alert('Viga: ' + error.message);
              }
            }
          }
        </script>
      </body>
      </html>
    `;

    return new Response(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });
  } catch (error) {
    console.error('Error loading maintenance management:', error);
    return c.text('Error loading maintenance management', 500);
  }
});

// Issue detail view for administrators
pageRoutes.get('/admin/issues/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const issue = await Issue.findById(id);

    if (!issue) {
      return c.html(`
        <!DOCTYPE html>
        <html lang="et">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>CMMS - Rike ei leitud</title>
          <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100 min-h-screen flex items-center justify-center">
          <div class="max-w-md w-full mx-4">
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
              <div class="alert-error bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                Rike ei leitud
              </div>
              <div class="text-gray-600 mb-4">
                Rike ID "${id}" ei ole süsteemis registreeritud.
              </div>
              <a href="/admin/issues" class="inline-block bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
                Tagasi rikete nimekirja
              </a>
            </div>
          </div>
        </body>
        </html>
      `, 404);
    }

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Rike #${issue.id}</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          <!-- Header -->
          <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">🚨 Rike #${issue.id}</h1>
            <div class="space-x-2">
              <a href="/admin/issues" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">
                ← Tagasi nimekirja
              </a>
              <button onclick="updateIssueStatus(${issue.id}, '${issue.status}')"
                      class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                Muuda staatust
              </button>
            </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Issue Details -->
            <div class="lg:col-span-2 space-y-6">
              <!-- Issue Information -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Rike andmed</h2>
                <div class="space-y-4">
                  <div>
                    <h3 class="text-lg font-medium text-gray-900">${issue.title}</h3>
                    <div class="flex items-center space-x-4 mt-2">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        issue.priority === 'critical' ? 'bg-red-100 text-red-800' :
                        issue.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                        issue.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }">
                        ${issue.priority} prioriteet
                      </span>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        issue.status === 'open' ? 'bg-red-100 text-red-800' :
                        issue.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' :
                        issue.status === 'resolved' ? 'bg-green-100 text-green-800' :
                        'bg-gray-100 text-gray-800'
                      }">
                        ${issue.status}
                      </span>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                        ${issue.issue_type}
                      </span>
                    </div>
                  </div>

                  ${issue.description ? `
                    <div>
                      <h4 class="text-sm font-medium text-gray-700 mb-2">Kirjeldus</h4>
                      <p class="text-gray-900 whitespace-pre-wrap">${issue.description}</p>
                    </div>
                  ` : ''}

                  ${issue.resolution_notes ? `
                    <div>
                      <h4 class="text-sm font-medium text-gray-700 mb-2">Lahenduse märkused</h4>
                      <p class="text-gray-900 whitespace-pre-wrap">${issue.resolution_notes}</p>
                    </div>
                  ` : ''}
                </div>
              </div>

              <!-- Resolution Form -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Lahenduse märkused</h2>
                <form id="resolutionForm" class="space-y-4">
                  <div>
                    <label for="resolution_notes" class="block text-sm font-medium text-gray-700 mb-2">
                      Lahenduse kirjeldus
                    </label>
                    <textarea name="resolution_notes"
                              id="resolution_notes"
                              rows="4"
                              placeholder="Kirjelda, kuidas rike lahendati..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">${issue.resolution_notes || ''}</textarea>
                  </div>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label for="assigned_to" class="block text-sm font-medium text-gray-700 mb-2">
                        Määratud isik
                      </label>
                      <input type="text"
                             name="assigned_to"
                             id="assigned_to"
                             value="${issue.assigned_to || ''}"
                             placeholder="Nimi või meeskond"
                             class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                    </div>
                    <div>
                      <label for="partner_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Väline partner
                      </label>
                      <select name="partner_id" id="partner_id"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                        <option value="">Vali partner...</option>
                      </select>
                    </div>
                  </div>
                  <div>
                    <label for="partner_notes" class="block text-sm font-medium text-gray-700 mb-2">
                      Märkused partnerile
                    </label>
                    <textarea name="partner_notes"
                              id="partner_notes"
                              rows="3"
                              placeholder="Erilised juhised või märkused partnerile..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">${issue.partner_notes || ''}</textarea>
                  </div>
                  <button type="submit" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
                    Salvesta märkused
                  </button>
                </form>
              </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
              <!-- Machine Information -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Masin</h2>
                <dl class="space-y-2">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Number</dt>
                    <dd class="text-sm text-gray-900">${issue.machine_number}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Nimi</dt>
                    <dd class="text-sm text-gray-900">${issue.machine_name}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Asukoht</dt>
                    <dd class="text-sm text-gray-900">${issue.machine_location || 'Määramata'}</dd>
                  </div>
                </dl>
                <a href="/machines/${issue.machine_id}" class="mt-4 inline-block bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm">
                  Vaata masinat
                </a>
              </div>

              <!-- Operator Information -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Operaator</h2>
                <dl class="space-y-2">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Number</dt>
                    <dd class="text-sm text-gray-900">${issue.operator_number}</dd>
                  </div>
                  ${issue.operator_name ? `
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Nimi</dt>
                      <dd class="text-sm text-gray-900">${issue.operator_name}</dd>
                    </div>
                  ` : ''}
                  ${issue.assigned_to ? `
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Määratud</dt>
                      <dd class="text-sm text-gray-900">${issue.assigned_to}</dd>
                    </div>
                  ` : ''}
                </dl>
              </div>

              ${issue.partner_company ? `
                <!-- Partner Information -->
                <div class="bg-white rounded-lg shadow-md p-6">
                  <h2 class="text-xl font-semibold mb-4">🤝 Väline partner</h2>
                  <dl class="space-y-2">
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Ettevõte</dt>
                      <dd class="text-sm text-gray-900 font-medium">${issue.partner_company}</dd>
                    </div>
                    ${issue.partner_contact ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">Kontaktisik</dt>
                        <dd class="text-sm text-gray-900">${issue.partner_contact}</dd>
                      </div>
                    ` : ''}
                    ${issue.partner_email ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">E-post</dt>
                        <dd class="text-sm text-gray-900">
                          <a href="mailto:${issue.partner_email}" class="text-blue-600 hover:text-blue-800">
                            ${issue.partner_email}
                          </a>
                        </dd>
                      </div>
                    ` : ''}
                    ${issue.partner_phone ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">Telefon</dt>
                        <dd class="text-sm text-gray-900">
                          <a href="tel:${issue.partner_phone}" class="text-blue-600 hover:text-blue-800">
                            ${issue.partner_phone}
                          </a>
                        </dd>
                      </div>
                    ` : ''}
                    ${issue.partner_notes ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">Märkused</dt>
                        <dd class="text-sm text-gray-900 bg-yellow-50 border border-yellow-200 rounded p-2">
                          ${issue.partner_notes}
                        </dd>
                      </div>
                    ` : ''}
                  </dl>
                </div>
              ` : ''}

              <!-- Timeline -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Ajalugu</h2>
                <dl class="space-y-2">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Teatatud</dt>
                    <dd class="text-sm text-gray-900">${new Date(issue.reported_at).toLocaleString('et-EE')}</dd>
                  </div>
                  ${issue.resolved_at ? `
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Lahendatud</dt>
                      <dd class="text-sm text-gray-900">${new Date(issue.resolved_at).toLocaleString('et-EE')}</dd>
                    </div>
                  ` : ''}
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Viimati uuendatud</dt>
                    <dd class="text-sm text-gray-900">${new Date(issue.updated_at).toLocaleString('et-EE')}</dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <script>
          // Load partners on page load
          document.addEventListener('DOMContentLoaded', function() {
            loadPartners();
          });

          async function loadPartners() {
            try {
              const response = await fetch('/api/partners?is_active=true');
              const partners = await response.json();

              const partnerSelect = document.getElementById('partner_id');
              partnerSelect.innerHTML = '<option value="">Vali partner...</option>';

              partners.forEach(partner => {
                const option = document.createElement('option');
                option.value = partner.id;
                option.textContent = \`\${partner.company_name} - \${partner.specializations.join(', ')}\`;

                // Set selected if this partner is already assigned
                if (partner.id == ${issue.partner_id || 'null'}) {
                  option.selected = true;
                }

                partnerSelect.appendChild(option);
              });
            } catch (error) {
              console.error('Error loading partners:', error);
            }
          }

          async function updateIssueStatus(issueId, currentStatus) {
            const statuses = ['open', 'in_progress', 'resolved', 'closed'];
            const statusLabels = {
              'open': 'Avatud',
              'in_progress': 'Töös',
              'resolved': 'Lahendatud',
              'closed': 'Suletud'
            };

            const newStatus = prompt(
              'Vali uus staatus:\\n' +
              statuses.map((s, i) => \`\${i + 1}. \${statusLabels[s]}\`).join('\\n'),
              statuses.indexOf(currentStatus) + 1
            );

            if (newStatus && newStatus >= 1 && newStatus <= 4) {
              const selectedStatus = statuses[newStatus - 1];

              try {
                const response = await fetch(\`/api/issues/\${issueId}\`, {
                  method: 'PUT',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ status: selectedStatus })
                });

                if (response.ok) {
                  alert('Staatus edukalt uuendatud!');
                  window.location.reload();
                } else {
                  alert('Viga staatuse uuendamisel!');
                }
              } catch (error) {
                alert('Viga: ' + error.message);
              }
            }
          }

          document.getElementById('resolutionForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const updateData = Object.fromEntries(formData.entries());

            try {
              const response = await fetch(\`/api/issues/${issue.id}\`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(updateData)
              });

              if (response.ok) {
                alert('Märkused edukalt salvestatud!');
                window.location.reload();
              } else {
                alert('Viga märkuste salvestamisel!');
              }
            } catch (error) {
              alert('Viga: ' + error.message);
            }
          });
        </script>
      </body>
      </html>
    `;

    return new Response(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });
  } catch (error) {
    console.error('Error loading issue detail:', error);
    return c.text('Error loading issue detail', 500);
  }
});

// Maintenance detail view for administrators
pageRoutes.get('/admin/maintenance/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const maintenanceRequest = await MaintenanceRequest.findById(id);

    if (!maintenanceRequest) {
      return c.html(`
        <!DOCTYPE html>
        <html lang="et">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>CMMS - Hoolduse taotlus ei leitud</title>
          <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100 min-h-screen flex items-center justify-center">
          <div class="max-w-md w-full mx-4">
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
              <div class="alert-error bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                Hoolduse taotlus ei leitud
              </div>
              <div class="text-gray-600 mb-4">
                Hoolduse taotlus ID "${id}" ei ole süsteemis registreeritud.
              </div>
              <a href="/admin/maintenance" class="inline-block bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded">
                Tagasi hoolduse nimekirja
              </a>
            </div>
          </div>
        </body>
        </html>
      `, 404);
    }

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Hoolduse taotlus #${maintenanceRequest.id}</title>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
          <!-- Header -->
          <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">🔧 Hoolduse taotlus #${maintenanceRequest.id}</h1>
            <div class="space-x-2">
              <a href="/admin/maintenance" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">
                ← Tagasi nimekirja
              </a>
              <button onclick="updateMaintenanceStatus(${maintenanceRequest.id}, '${maintenanceRequest.status}')"
                      class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                Muuda staatust
              </button>
            </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Maintenance Details -->
            <div class="lg:col-span-2 space-y-6">
              <!-- Maintenance Information -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Hoolduse andmed</h2>
                <div class="space-y-4">
                  <div>
                    <h3 class="text-lg font-medium text-gray-900">${maintenanceRequest.title}</h3>
                    <div class="flex items-center space-x-4 mt-2">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        maintenanceRequest.maintenance_type === 'emergency' ? 'bg-red-100 text-red-800' :
                        maintenanceRequest.maintenance_type === 'preventive' ? 'bg-green-100 text-green-800' :
                        maintenanceRequest.maintenance_type === 'corrective' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-blue-100 text-blue-800'
                      }">
                        ${maintenanceRequest.maintenance_type}
                      </span>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        maintenanceRequest.urgency === 'urgent' ? 'bg-red-100 text-red-800' :
                        maintenanceRequest.urgency === 'high' ? 'bg-orange-100 text-orange-800' :
                        maintenanceRequest.urgency === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }">
                        ${maintenanceRequest.urgency}
                      </span>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        maintenanceRequest.status === 'requested' ? 'bg-yellow-100 text-yellow-800' :
                        maintenanceRequest.status === 'scheduled' ? 'bg-blue-100 text-blue-800' :
                        maintenanceRequest.status === 'in_progress' ? 'bg-orange-100 text-orange-800' :
                        maintenanceRequest.status === 'completed' ? 'bg-green-100 text-green-800' :
                        'bg-gray-100 text-gray-800'
                      }">
                        ${maintenanceRequest.status}
                      </span>
                    </div>
                  </div>

                  ${maintenanceRequest.description ? `
                    <div>
                      <h4 class="text-sm font-medium text-gray-700 mb-2">Kirjeldus</h4>
                      <p class="text-gray-900 whitespace-pre-wrap">${maintenanceRequest.description}</p>
                    </div>
                  ` : ''}

                  ${maintenanceRequest.maintenance_notes ? `
                    <div>
                      <h4 class="text-sm font-medium text-gray-700 mb-2">Hoolduse märkused</h4>
                      <p class="text-gray-900 whitespace-pre-wrap">${maintenanceRequest.maintenance_notes}</p>
                    </div>
                  ` : ''}

                  ${maintenanceRequest.completion_notes ? `
                    <div>
                      <h4 class="text-sm font-medium text-gray-700 mb-2">Lõpetamise märkused</h4>
                      <p class="text-gray-900 whitespace-pre-wrap">${maintenanceRequest.completion_notes}</p>
                    </div>
                  ` : ''}
                </div>
              </div>

              <!-- Maintenance Management Form -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Hoolduse haldamine</h2>
                <form id="maintenanceForm" class="space-y-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label for="assigned_to" class="block text-sm font-medium text-gray-700 mb-2">
                        Määratud isik/meeskond
                      </label>
                      <input type="text"
                             name="assigned_to"
                             id="assigned_to"
                             value="${maintenanceRequest.assigned_to || ''}"
                             placeholder="Nimi või meeskond"
                             class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                    </div>
                    <div>
                      <label for="partner_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Väline partner
                      </label>
                      <select name="partner_id" id="partner_id"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                        <option value="">Vali partner...</option>
                      </select>
                    </div>
                    <div>
                      <label for="scheduled_date" class="block text-sm font-medium text-gray-700 mb-2">
                        Planeeritud kuupäev
                      </label>
                      <input type="date"
                             name="scheduled_date"
                             id="scheduled_date"
                             value="${maintenanceRequest.scheduled_date ? new Date(maintenanceRequest.scheduled_date).toISOString().split('T')[0] : ''}"
                             class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                    </div>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label for="estimated_duration" class="block text-sm font-medium text-gray-700 mb-2">
                        Hinnanguline kestus (minutites)
                      </label>
                      <input type="number"
                             name="estimated_duration"
                             id="estimated_duration"
                             value="${maintenanceRequest.estimated_duration || ''}"
                             placeholder="60"
                             class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                    </div>
                    <div>
                      <label for="cost" class="block text-sm font-medium text-gray-700 mb-2">
                        Maksumus (€)
                      </label>
                      <input type="number"
                             name="cost"
                             id="cost"
                             step="0.01"
                             value="${maintenanceRequest.cost || ''}"
                             placeholder="0.00"
                             class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
                    </div>
                  </div>

                  <div>
                    <label for="partner_notes" class="block text-sm font-medium text-gray-700 mb-2">
                      Märkused partnerile
                    </label>
                    <textarea name="partner_notes"
                              id="partner_notes"
                              rows="2"
                              placeholder="Erilised juhised või märkused partnerile..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">${maintenanceRequest.partner_notes || ''}</textarea>
                  </div>

                  <div>
                    <label for="maintenance_notes" class="block text-sm font-medium text-gray-700 mb-2">
                      Hoolduse märkused
                    </label>
                    <textarea name="maintenance_notes"
                              id="maintenance_notes"
                              rows="3"
                              placeholder="Hoolduse käigus tehtud märkused..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">${maintenanceRequest.maintenance_notes || ''}</textarea>
                  </div>

                  <div>
                    <label for="completion_notes" class="block text-sm font-medium text-gray-700 mb-2">
                      Lõpetamise märkused
                    </label>
                    <textarea name="completion_notes"
                              id="completion_notes"
                              rows="3"
                              placeholder="Hoolduse lõpetamise märkused..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">${maintenanceRequest.completion_notes || ''}</textarea>
                  </div>

                  <button type="submit" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded">
                    Salvesta andmed
                  </button>
                </form>
              </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
              <!-- Machine Information -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Masin</h2>
                <dl class="space-y-2">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Number</dt>
                    <dd class="text-sm text-gray-900">${maintenanceRequest.machine_number}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Nimi</dt>
                    <dd class="text-sm text-gray-900">${maintenanceRequest.machine_name}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Asukoht</dt>
                    <dd class="text-sm text-gray-900">${maintenanceRequest.machine_location || 'Määramata'}</dd>
                  </div>
                </dl>
                <a href="/machines/${maintenanceRequest.machine_id}" class="mt-4 inline-block bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm">
                  Vaata masinat
                </a>
              </div>

              <!-- Operator Information -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Operaator</h2>
                <dl class="space-y-2">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Number</dt>
                    <dd class="text-sm text-gray-900">${maintenanceRequest.operator_number}</dd>
                  </div>
                  ${maintenanceRequest.operator_name ? `
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Nimi</dt>
                      <dd class="text-sm text-gray-900">${maintenanceRequest.operator_name}</dd>
                    </div>
                  ` : ''}
                </dl>
              </div>

              <!-- Partner Information -->
              ${maintenanceRequest.partner_company ? `
                <div class="bg-white rounded-lg shadow-md p-6">
                  <h2 class="text-xl font-semibold mb-4">Väline Partner</h2>
                  <dl class="space-y-2">
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Ettevõte</dt>
                      <dd class="text-sm text-gray-900">${maintenanceRequest.partner_company}</dd>
                    </div>
                    ${maintenanceRequest.partner_contact ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">Kontaktisik</dt>
                        <dd class="text-sm text-gray-900">${maintenanceRequest.partner_contact}</dd>
                      </div>
                    ` : ''}
                    ${maintenanceRequest.partner_email ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">E-post</dt>
                        <dd class="text-sm text-gray-900">
                          <a href="mailto:${maintenanceRequest.partner_email}" class="text-blue-600 hover:text-blue-800">
                            ${maintenanceRequest.partner_email}
                          </a>
                        </dd>
                      </div>
                    ` : ''}
                    ${maintenanceRequest.partner_phone ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">Telefon</dt>
                        <dd class="text-sm text-gray-900">
                          <a href="tel:${maintenanceRequest.partner_phone}" class="text-blue-600 hover:text-blue-800">
                            ${maintenanceRequest.partner_phone}
                          </a>
                        </dd>
                      </div>
                    ` : ''}
                    ${maintenanceRequest.partner_notes ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">Märkused partnerile</dt>
                        <dd class="text-sm text-gray-900">${maintenanceRequest.partner_notes}</dd>
                      </div>
                    ` : ''}
                  </dl>
                </div>
              ` : ''}

              <!-- Timeline -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Ajalugu</h2>
                <dl class="space-y-2">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Taotletud</dt>
                    <dd class="text-sm text-gray-900">${maintenanceRequest.requested_date ?
                      new Date(maintenanceRequest.requested_date).toLocaleDateString('et-EE') :
                      new Date(maintenanceRequest.created_at).toLocaleDateString('et-EE')}</dd>
                  </div>
                  ${maintenanceRequest.scheduled_date ? `
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Planeeritud</dt>
                      <dd class="text-sm text-gray-900">${new Date(maintenanceRequest.scheduled_date).toLocaleDateString('et-EE')}</dd>
                    </div>
                  ` : ''}
                  ${maintenanceRequest.completed_date ? `
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Lõpetatud</dt>
                      <dd class="text-sm text-gray-900">${new Date(maintenanceRequest.completed_date).toLocaleDateString('et-EE')}</dd>
                    </div>
                  ` : ''}
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Viimati uuendatud</dt>
                    <dd class="text-sm text-gray-900">${new Date(maintenanceRequest.updated_at).toLocaleString('et-EE')}</dd>
                  </div>
                </dl>
              </div>

              <!-- Duration & Cost -->
              ${maintenanceRequest.estimated_duration || maintenanceRequest.actual_duration || maintenanceRequest.cost ? `
                <div class="bg-white rounded-lg shadow-md p-6">
                  <h2 class="text-xl font-semibold mb-4">Kestus & Maksumus</h2>
                  <dl class="space-y-2">
                    ${maintenanceRequest.estimated_duration ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">Hinnanguline kestus</dt>
                        <dd class="text-sm text-gray-900">${maintenanceRequest.estimated_duration} min</dd>
                      </div>
                    ` : ''}
                    ${maintenanceRequest.actual_duration ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">Tegelik kestus</dt>
                        <dd class="text-sm text-gray-900">${maintenanceRequest.actual_duration} min</dd>
                      </div>
                    ` : ''}
                    ${maintenanceRequest.cost ? `
                      <div>
                        <dt class="text-sm font-medium text-gray-500">Maksumus</dt>
                        <dd class="text-sm text-gray-900">${maintenanceRequest.cost} €</dd>
                      </div>
                    ` : ''}
                  </dl>
                </div>
              ` : ''}
            </div>
          </div>
        </div>

        <script>
          async function updateMaintenanceStatus(requestId, currentStatus) {
            const statuses = ['requested', 'scheduled', 'in_progress', 'completed', 'cancelled'];
            const statusLabels = {
              'requested': 'Taotletud',
              'scheduled': 'Planeeritud',
              'in_progress': 'Töös',
              'completed': 'Lõpetatud',
              'cancelled': 'Tühistatud'
            };

            const newStatus = prompt(
              'Vali uus staatus:\\n' +
              statuses.map((s, i) => \`\${i + 1}. \${statusLabels[s]}\`).join('\\n'),
              statuses.indexOf(currentStatus) + 1
            );

            if (newStatus && newStatus >= 1 && newStatus <= 5) {
              const selectedStatus = statuses[newStatus - 1];

              try {
                const response = await fetch(\`/api/maintenance/\${requestId}\`, {
                  method: 'PUT',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ status: selectedStatus })
                });

                if (response.ok) {
                  alert('Staatus edukalt uuendatud!');
                  window.location.reload();
                } else {
                  alert('Viga staatuse uuendamisel!');
                }
              } catch (error) {
                alert('Viga: ' + error.message);
              }
            }
          }

          // Load partners on page load
          document.addEventListener('DOMContentLoaded', function() {
            loadPartners();
          });

          async function loadPartners() {
            try {
              const response = await fetch('/api/partners?is_active=true');
              const partners = await response.json();

              const partnerSelect = document.getElementById('partner_id');
              partnerSelect.innerHTML = '<option value="">Vali partner...</option>';

              partners.forEach(partner => {
                const option = document.createElement('option');
                option.value = partner.id;
                option.textContent = \`\${partner.company_name} - \${partner.specializations.join(', ')}\`;

                // Set selected if this partner is already assigned
                if (partner.id == ${maintenanceRequest.partner_id || 'null'}) {
                  option.selected = true;
                }

                partnerSelect.appendChild(option);
              });
            } catch (error) {
              console.error('Error loading partners:', error);
            }
          }

          document.getElementById('maintenanceForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const updateData = Object.fromEntries(formData.entries());

            // Convert empty strings to null for optional fields
            Object.keys(updateData).forEach(key => {
              if (updateData[key] === '') {
                updateData[key] = null;
              }
            });

            try {
              const response = await fetch(\`/api/maintenance/${maintenanceRequest.id}\`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(updateData)
              });

              if (response.ok) {
                alert('Andmed edukalt salvestatud!');
                window.location.reload();
              } else {
                alert('Viga andmete salvestamisel!');
              }
            } catch (error) {
              alert('Viga: ' + error.message);
            }
          });
        </script>
      </body>
      </html>
    `;

    return new Response(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });
  } catch (error) {
    console.error('Error loading maintenance detail:', error);
    return c.text('Error loading maintenance detail', 500);
  }
});

// Admin partners page
pageRoutes.get('/admin/partners', async (c) => {
  try {
    const html = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Hoolduspartnerid - CMMS</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
      </head>
      <body class="bg-gray-50">
        <div class="min-h-screen">
          <!-- Navigation -->
          <nav class="bg-blue-600 text-white">
            <div class="container mx-auto px-4">
              <!-- Desktop Navigation -->
              <div class="hidden md:flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                  <h1 class="text-xl font-bold">CMMS - Partnerid</h1>
                </div>
                <div class="flex space-x-4">
                  <a href="/admin" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                  </a>
                  <a href="/machines" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-cogs mr-2"></i>Masinad
                  </a>
                  <a href="/admin/machine-groups" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-layer-group mr-2"></i>Grupid
                  </a>
                  <a href="/admin/reports" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-chart-bar mr-2"></i>Aruanded
                  </a>
                  <a href="/admin/issues" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Rikked
                  </a>
                  <a href="/admin/maintenance" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-wrench mr-2"></i>Hooldus
                  </a>
                  <a href="/admin/partners" class="bg-blue-800 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-handshake mr-2"></i>Partnerid
                  </a>
                </div>
              </div>

              <!-- Mobile Navigation -->
              <div class="md:hidden">
                <div class="flex justify-between items-center py-3">
                  <h1 class="text-lg font-bold">CMMS - Partnerid</h1>
                  <button onclick="toggleMobileMenu()" class="p-2 rounded hover:bg-blue-700 min-h-[44px] min-w-[44px]">
                    <i id="mobile-menu-icon" class="fas fa-bars text-xl"></i>
                  </button>
                </div>

                <!-- Mobile Menu -->
                <div id="mobile-menu" class="hidden pb-4">
                  <div class="space-y-2">
                    <a href="/admin" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-home mr-2"></i>Dashboard
                    </a>
                    <a href="/machines" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-cogs mr-2"></i>Masinad
                    </a>
                    <a href="/admin/machine-groups" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-layer-group mr-2"></i>Grupid
                    </a>
                    <a href="/admin/reports" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-chart-bar mr-2"></i>Aruanded
                    </a>
                    <a href="/admin/issues" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-exclamation-triangle mr-2"></i>Rikked
                    </a>
                    <a href="/admin/maintenance" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-wrench mr-2"></i>Hooldus
                    </a>
                    <a href="/admin/partners" class="block bg-blue-800 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-handshake mr-2"></i>Partnerid
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </nav>

          <div class="container mx-auto px-4 py-8">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-2xl font-bold text-gray-800">Hoolduspartnerid</h2>
              <button onclick="openAddPartnerModal()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                <i class="fas fa-plus mr-2"></i>Lisa Partner
              </button>
            </div>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-handshake text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Kokku Partnereid</p>
                    <p class="text-2xl font-semibold text-gray-900" id="total-partners">-</p>
                  </div>
                </div>
              </div>

              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-check-circle text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Aktiivsed</p>
                    <p class="text-2xl font-semibold text-gray-900" id="active-partners">-</p>
                  </div>
                </div>
              </div>

              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                    <i class="fas fa-tasks text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Aktiivsed Taotlused</p>
                    <p class="text-2xl font-semibold text-gray-900" id="active-requests">-</p>
                  </div>
                </div>
              </div>

              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <i class="fas fa-euro-sign text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Keskmine Tunnitasu</p>
                    <p class="text-2xl font-semibold text-gray-900" id="avg-rate">-</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Partners Table -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ettevõte</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kontakt</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spetsialiseerumised</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tunnitasu</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aktiivsed Taotlused</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Staatus</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toimingud</th>
                    </tr>
                  </thead>
                  <tbody id="partners-table-body" class="bg-white divide-y divide-gray-200">
                    <!-- Partners will be loaded here -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <script>
          // Mobile menu toggle
          function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            const icon = document.getElementById('mobile-menu-icon');

            if (menu.classList.contains('hidden')) {
              menu.classList.remove('hidden');
              icon.classList.remove('fa-bars');
              icon.classList.add('fa-times');
            } else {
              menu.classList.add('hidden');
              icon.classList.remove('fa-times');
              icon.classList.add('fa-bars');
            }
          }

          // Close mobile menu when clicking outside
          document.addEventListener('click', function(event) {
            const menu = document.getElementById('mobile-menu');
            const button = event.target.closest('button[onclick="toggleMobileMenu()"]');

            if (!button && !menu.contains(event.target) && !menu.classList.contains('hidden')) {
              toggleMobileMenu();
            }
          });

          let partners = [];

          // Load data on page load
          document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadPartners();
          });

          async function loadStatistics() {
            try {
              const response = await fetch('/api/partners/statistics');
              const stats = await response.json();

              document.getElementById('total-partners').textContent = stats.total_partners || 0;
              document.getElementById('active-partners').textContent = stats.active_partners || 0;
              document.getElementById('active-requests').textContent = stats.total_partner_requests || 0;
              document.getElementById('avg-rate').textContent = stats.average_hourly_rate ?
                parseFloat(stats.average_hourly_rate).toFixed(2) + '€' : '-';
            } catch (error) {
              console.error('Error loading statistics:', error);
            }
          }

          async function loadPartners() {
            try {
              const response = await fetch('/api/partners');
              partners = await response.json();

              renderPartnersTable();
            } catch (error) {
              console.error('Error loading partners:', error);
            }
          }

          function renderPartnersTable() {
            const tbody = document.getElementById('partners-table-body');
            tbody.innerHTML = '';

            partners.forEach(partner => {
              const row = document.createElement('tr');
              row.className = 'hover:bg-gray-50';

              const statusBadge = partner.is_active ?
                '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Aktiivne</span>' :
                '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Mitteaktiivne</span>';

              const specialsBadges = partner.specializations.map(spec =>
                \`<span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">\${spec}</span>\`
              ).join(' ');

              row.innerHTML = \`
                <td class="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div class="text-sm font-medium text-gray-900">\${partner.company_name}</div>
                    <div class="text-sm text-gray-500">\${partner.email}</div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">\${partner.contact_person || '-'}</div>
                  <div class="text-sm text-gray-500">\${partner.phone || '-'}</div>
                </td>
                <td class="px-6 py-4">
                  <div class="flex flex-wrap gap-1">\${specialsBadges}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  \${partner.hourly_rate ? parseFloat(partner.hourly_rate).toFixed(2) + '€' : '-'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  \${partner.active_requests}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  \${statusBadge}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button onclick="viewPartnerRequests(\${partner.id})" class="text-green-600 hover:text-green-900 mr-3" title="Vaata taotlusi">
                    <i class="fas fa-tasks"></i>
                  </button>
                  <button onclick="editPartner(\${partner.id})" class="text-blue-600 hover:text-blue-900 mr-3" title="Muuda">
                    <i class="fas fa-edit"></i>
                  </button>
                  \${partner.is_active ?
                    \`<button onclick="togglePartnerStatus(\${partner.id}, false)" class="text-orange-600 hover:text-orange-900 mr-3" title="Deaktiveeri">
                      <i class="fas fa-pause"></i>
                    </button>\` :
                    \`<button onclick="togglePartnerStatus(\${partner.id}, true)" class="text-green-600 hover:text-green-900 mr-3" title="Aktiveeri">
                      <i class="fas fa-play"></i>
                    </button>\`
                  }
                  <button onclick="deletePartner(\${partner.id})" class="text-red-600 hover:text-red-900" title="Kustuta">
                    <i class="fas fa-trash"></i>
                  </button>
                </td>
              \`;

              tbody.appendChild(row);
            });
          }

          function openAddPartnerModal() {
            alert('Partner modal tuleb järgmises versioonis!');
          }

          function editPartner(id) {
            alert(\`Partneri \${id} muutmine tuleb järgmises versioonis!\`);
          }

          async function togglePartnerStatus(id, activate) {
            try {
              const action = activate ? 'activate' : 'deactivate';
              const response = await fetch(\`/api/partners/\${id}/\${action}\`, {
                method: 'POST'
              });

              if (response.ok) {
                loadPartners();
                loadStatistics();
                alert(\`Partner \${activate ? 'aktiveeritud' : 'deaktiveeritud'}!\`);
              } else {
                const error = await response.json();
                alert('Viga: ' + (error.error || 'Teadmata viga'));
              }
            } catch (error) {
              console.error('Error toggling partner status:', error);
              alert('Viga partneri staatuse muutmisel');
            }
          }

          async function deletePartner(id) {
            if (!confirm('Kas olete kindel, et soovite selle partneri kustutada?')) {
              return;
            }

            try {
              const response = await fetch(\`/api/partners/\${id}\`, {
                method: 'DELETE'
              });

              if (response.ok) {
                loadPartners();
                loadStatistics();
                alert('Partner edukalt kustutatud!');
              } else {
                const error = await response.json();
                alert('Viga: ' + (error.error || 'Teadmata viga'));
              }
            } catch (error) {
              console.error('Error deleting partner:', error);
              alert('Viga partneri kustutamisel');
            }
          }

          function viewPartnerRequests(id) {
            window.open(\`/admin/partners/\${id}/requests\`, '_blank');
          }
        </script>
      </body>
      </html>
    `;

    return c.html(html);
  } catch (error) {
    console.error('Error loading partners page:', error);
    return c.text('Error loading page', 500);
  }
});

// Admin partner requests page
pageRoutes.get('/admin/partners/:id/requests', async (c) => {
  try {
    const partnerId = parseInt(c.req.param('id'));

    const html = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Partneri Taotlused - CMMS</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
      </head>
      <body class="bg-gray-50">
        <div class="min-h-screen">
          <!-- Navigation -->
          <nav class="bg-blue-600 text-white p-4">
            <div class="container mx-auto flex justify-between items-center">
              <div class="flex items-center space-x-4">
                <h1 class="text-xl font-bold">CMMS - Partneri Taotlused</h1>
              </div>
              <div class="flex space-x-4">
                <a href="/admin" class="hover:bg-blue-700 px-3 py-2 rounded">
                  <i class="fas fa-home mr-2"></i>Dashboard
                </a>
                <a href="/admin/partners" class="hover:bg-blue-700 px-3 py-2 rounded">
                  <i class="fas fa-handshake mr-2"></i>Tagasi Partneritele
                </a>
              </div>
            </div>
          </nav>

          <div class="container mx-auto px-4 py-8">
            <!-- Partner Info Header -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
              <div class="flex items-center justify-between">
                <div>
                  <h2 class="text-2xl font-bold text-gray-800" id="partner-name">Laadin...</h2>
                  <p class="text-gray-600" id="partner-email">-</p>
                </div>
                <div class="text-right">
                  <p class="text-sm text-gray-500">Kontakt</p>
                  <p class="text-lg font-semibold" id="partner-contact">-</p>
                  <p class="text-gray-600" id="partner-phone">-</p>
                </div>
              </div>
              <div class="mt-4">
                <p class="text-sm text-gray-500">Spetsialiseerumised</p>
                <div id="partner-specializations" class="flex flex-wrap gap-2 mt-1">
                  <!-- Specializations will be loaded here -->
                </div>
              </div>
            </div>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-tasks text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Kokku Taotlusi</p>
                    <p class="text-2xl font-semibold text-gray-900" id="total-requests">-</p>
                  </div>
                </div>
              </div>

              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                    <i class="fas fa-clock text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Ootel</p>
                    <p class="text-2xl font-semibold text-gray-900" id="pending-requests">-</p>
                  </div>
                </div>
              </div>

              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-calendar text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Planeeritud</p>
                    <p class="text-2xl font-semibold text-gray-900" id="scheduled-requests">-</p>
                  </div>
                </div>
              </div>

              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-check-circle text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Lõpetatud</p>
                    <p class="text-2xl font-semibold text-gray-900" id="completed-requests">-</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Filters -->
            <div class="bg-white p-4 rounded-lg shadow mb-6">
              <div class="flex flex-wrap gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Staatus</label>
                  <select id="status-filter" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Kõik</option>
                    <option value="requested">Taotletud</option>
                    <option value="scheduled">Planeeritud</option>
                    <option value="in_progress">Töös</option>
                    <option value="completed">Lõpetatud</option>
                    <option value="cancelled">Tühistatud</option>
                  </select>
                </div>
                <div class="flex items-end">
                  <button onclick="loadRequests()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    <i class="fas fa-search mr-2"></i>Otsi
                  </button>
                </div>
              </div>
            </div>

            <!-- Requests Table -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Taotlus</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Masin</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tüüp</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kiireloomulisus</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kuupäevad</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Staatus</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toimingud</th>
                    </tr>
                  </thead>
                  <tbody id="requests-table-body" class="bg-white divide-y divide-gray-200">
                    <!-- Requests will be loaded here -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <script>
          const partnerId = ${partnerId};
          let partner = null;
          let requests = [];

          // Load data on page load
          document.addEventListener('DOMContentLoaded', function() {
            loadPartner();
            loadRequests();
          });

          async function loadPartner() {
            try {
              const response = await fetch(\`/api/partners/\${partnerId}\`);
              partner = await response.json();

              document.getElementById('partner-name').textContent = partner.company_name;
              document.getElementById('partner-email').textContent = partner.email;
              document.getElementById('partner-contact').textContent = partner.contact_person || '-';
              document.getElementById('partner-phone').textContent = partner.phone || '-';

              // Update specializations
              const specializationsContainer = document.getElementById('partner-specializations');
              specializationsContainer.innerHTML = '';
              partner.specializations.forEach(spec => {
                const badge = document.createElement('span');
                badge.className = 'px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded';
                badge.textContent = spec;
                specializationsContainer.appendChild(badge);
              });
            } catch (error) {
              console.error('Error loading partner:', error);
            }
          }

          async function loadRequests() {
            try {
              const status = document.getElementById('status-filter').value;

              let url = \`/api/partners/\${partnerId}/requests\`;
              if (status) {
                url += \`?status=\${status}\`;
              }

              const response = await fetch(url);
              requests = await response.json();

              updateStatistics();
              renderRequestsTable();
            } catch (error) {
              console.error('Error loading requests:', error);
            }
          }

          function updateStatistics() {
            const stats = {
              total: requests.length,
              requested: requests.filter(r => r.status === 'requested').length,
              scheduled: requests.filter(r => r.status === 'scheduled').length,
              in_progress: requests.filter(r => r.status === 'in_progress').length,
              completed: requests.filter(r => r.status === 'completed').length
            };

            document.getElementById('total-requests').textContent = stats.total;
            document.getElementById('pending-requests').textContent = stats.requested;
            document.getElementById('scheduled-requests').textContent = stats.scheduled;
            document.getElementById('completed-requests').textContent = stats.completed;
          }

          function renderRequestsTable() {
            const tbody = document.getElementById('requests-table-body');
            tbody.innerHTML = '';

            if (requests.length === 0) {
              const row = document.createElement('tr');
              row.innerHTML = \`
                <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                  Sellel partneril pole veel taotlusi
                </td>
              \`;
              tbody.appendChild(row);
              return;
            }

            requests.forEach(request => {
              const row = document.createElement('tr');
              row.className = 'hover:bg-gray-50';

              const statusColors = {
                'requested': 'bg-yellow-100 text-yellow-800',
                'scheduled': 'bg-blue-100 text-blue-800',
                'in_progress': 'bg-orange-100 text-orange-800',
                'completed': 'bg-green-100 text-green-800',
                'cancelled': 'bg-red-100 text-red-800'
              };

              const urgencyColors = {
                'low': 'bg-green-100 text-green-800',
                'medium': 'bg-yellow-100 text-yellow-800',
                'high': 'bg-orange-100 text-orange-800',
                'urgent': 'bg-red-100 text-red-800'
              };

              const typeColors = {
                'preventive': 'bg-green-100 text-green-800',
                'corrective': 'bg-yellow-100 text-yellow-800',
                'emergency': 'bg-red-100 text-red-800',
                'inspection': 'bg-blue-100 text-blue-800',
                'calibration': 'bg-purple-100 text-purple-800',
                'cleaning': 'bg-cyan-100 text-cyan-800',
                'other': 'bg-gray-100 text-gray-800'
              };

              row.innerHTML = \`
                <td class="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div class="text-sm font-medium text-gray-900">\${request.title}</div>
                    <div class="text-sm text-gray-500">ID: \${request.id}</div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div class="text-sm font-medium text-gray-900">\${request.machine_number}</div>
                    <div class="text-sm text-gray-500">\${request.machine_name}</div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs font-semibold rounded-full \${typeColors[request.maintenance_type] || 'bg-gray-100 text-gray-800'}">
                    \${request.maintenance_type}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs font-semibold rounded-full \${urgencyColors[request.urgency] || 'bg-gray-100 text-gray-800'}">
                    \${request.urgency}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>Taotletud: \${new Date(request.created_at).toLocaleDateString('et-EE')}</div>
                  \${request.scheduled_date ? \`<div>Planeeritud: \${new Date(request.scheduled_date).toLocaleDateString('et-EE')}</div>\` : ''}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs font-semibold rounded-full \${statusColors[request.status] || 'bg-gray-100 text-gray-800'}">
                    \${request.status}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <a href="/admin/maintenance/\${request.id}" class="text-blue-600 hover:text-blue-900" title="Vaata detaile">
                    <i class="fas fa-eye"></i>
                  </a>
                </td>
              \`;

              tbody.appendChild(row);
            });
          }

          // Filter on change
          document.getElementById('status-filter').addEventListener('change', loadRequests);
        </script>
      </body>
      </html>
    `;

    return c.html(html);
  } catch (error) {
    console.error('Error loading partner requests page:', error);
    return c.text('Error loading page', 500);
  }
});

// Machine Groups management page
pageRoutes.get('/admin/machine-groups', async (c) => {
  try {
    const html = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Masinate Grupid - CMMS</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
      </head>
      <body class="bg-gray-50">
        <div class="min-h-screen">
          <!-- Navigation -->
          <nav class="bg-blue-600 text-white">
            <div class="container mx-auto px-4">
              <!-- Desktop Navigation -->
              <div class="hidden md:flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                  <h1 class="text-xl font-bold">CMMS - Grupid</h1>
                </div>
                <div class="flex space-x-4">
                  <a href="/admin" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                  </a>
                  <a href="/machines" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-cogs mr-2"></i>Masinad
                  </a>
                  <a href="/admin/machine-groups" class="bg-blue-800 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-layer-group mr-2"></i>Grupid
                  </a>
                  <a href="/admin/reports" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-chart-bar mr-2"></i>Aruanded
                  </a>
                  <a href="/admin/issues" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Rikked
                  </a>
                  <a href="/admin/maintenance" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-wrench mr-2"></i>Hooldus
                  </a>
                  <a href="/admin/partners" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-handshake mr-2"></i>Partnerid
                  </a>
                </div>
              </div>

              <!-- Mobile Navigation -->
              <div class="md:hidden">
                <div class="flex justify-between items-center py-3">
                  <h1 class="text-lg font-bold">CMMS - Grupid</h1>
                  <button onclick="toggleMobileMenu()" class="p-2 rounded hover:bg-blue-700 min-h-[44px] min-w-[44px]">
                    <i id="mobile-menu-icon" class="fas fa-bars text-xl"></i>
                  </button>
                </div>

                <!-- Mobile Menu -->
                <div id="mobile-menu" class="hidden pb-4">
                  <div class="space-y-2">
                    <a href="/admin" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-home mr-2"></i>Dashboard
                    </a>
                    <a href="/machines" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-cogs mr-2"></i>Masinad
                    </a>
                    <a href="/admin/machine-groups" class="block bg-blue-800 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-layer-group mr-2"></i>Grupid
                    </a>
                    <a href="/admin/reports" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-chart-bar mr-2"></i>Aruanded
                    </a>
                    <a href="/admin/issues" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-exclamation-triangle mr-2"></i>Rikked
                    </a>
                    <a href="/admin/maintenance" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-wrench mr-2"></i>Hooldus
                    </a>
                    <a href="/admin/partners" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-handshake mr-2"></i>Partnerid
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </nav>

          <div class="container mx-auto px-4 py-8">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-2xl font-bold text-gray-800">Masinate Grupid</h2>
              <div class="flex gap-3">
                <button onclick="showCreateGroupModal()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 min-h-[44px] flex items-center">
                  <i class="fas fa-plus mr-2"></i>Lisa Grupp
                </button>
                <button onclick="showGroupManagement()" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 min-h-[44px] flex items-center">
                  <i class="fas fa-cogs mr-2"></i>Halda Gruppe
                </button>
              </div>
            </div>

            <!-- Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-layer-group text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Kokku Gruppe</p>
                    <p class="text-2xl font-semibold text-gray-900" id="total-groups">-</p>
                  </div>
                </div>
              </div>

              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-check-circle text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Aktiivsed</p>
                    <p class="text-2xl font-semibold text-gray-900" id="active-groups">-</p>
                  </div>
                </div>
              </div>

              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <i class="fas fa-cogs text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Masinad Gruppides</p>
                    <p class="text-2xl font-semibold text-gray-900" id="machines-in-groups">-</p>
                  </div>
                </div>
              </div>

              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                    <i class="fas fa-exclamation-triangle text-xl"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Ilma Grupita</p>
                    <p class="text-2xl font-semibold text-gray-900" id="ungrouped-machines">-</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Groups Grid -->
            <div id="groups-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <!-- Groups will be loaded here -->
            </div>
          </div>
        </div>

        <!-- Create/Edit Group Modal -->
        <div id="group-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
          <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
              <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                  <h3 id="modal-title" class="text-lg font-semibold">Lisa Uus Grupp</h3>
                  <button onclick="hideGroupModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                  </button>
                </div>

                <form id="group-form">
                  <input type="hidden" id="group-id" name="id">

                  <div class="mb-4">
                    <label for="group-name" class="block text-sm font-medium text-gray-700 mb-2">Grupi Nimi *</label>
                    <input type="text" id="group-name" name="name" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                  </div>

                  <div class="mb-4">
                    <label for="group-description" class="block text-sm font-medium text-gray-700 mb-2">Kirjeldus</label>
                    <textarea id="group-description" name="description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"></textarea>
                  </div>

                  <div class="mb-4">
                    <label for="group-color" class="block text-sm font-medium text-gray-700 mb-2">Värv</label>
                    <div class="flex flex-wrap gap-2" id="color-options">
                      <!-- Color options will be loaded here -->
                    </div>
                    <input type="hidden" id="group-color" name="color" value="#3B82F6">
                  </div>

                  <div class="mb-6">
                    <label for="group-icon" class="block text-sm font-medium text-gray-700 mb-2">Ikoon</label>
                    <div class="grid grid-cols-5 gap-2" id="icon-options">
                      <!-- Icon options will be loaded here -->
                    </div>
                    <input type="hidden" id="group-icon" name="icon" value="fas fa-cogs">
                  </div>

                  <div class="flex justify-end space-x-3">
                    <button type="button" onclick="hideGroupModal()"
                            class="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50">
                      Tühista
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                      Salvesta
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <script>
          // Mobile menu toggle
          function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            const icon = document.getElementById('mobile-menu-icon');

            if (menu.classList.contains('hidden')) {
              menu.classList.remove('hidden');
              icon.classList.remove('fa-bars');
              icon.classList.add('fa-times');
            } else {
              menu.classList.add('hidden');
              icon.classList.remove('fa-times');
              icon.classList.add('fa-bars');
            }
          }

          // Close mobile menu when clicking outside
          document.addEventListener('click', function(event) {
            const menu = document.getElementById('mobile-menu');
            const button = event.target.closest('button[onclick="toggleMobileMenu()"]');

            if (!button && !menu.contains(event.target) && !menu.classList.contains('hidden')) {
              toggleMobileMenu();
            }
          });

          let groups = [];
          let availableColors = [];
          let availableIcons = [];

          // Load data on page load
          document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadGroups();
            loadColorOptions();
            loadIconOptions();
          });

          async function loadStatistics() {
            try {
              const response = await fetch('/api/machine-groups/statistics');
              const stats = await response.json();

              document.getElementById('total-groups').textContent = stats.total_groups || 0;
              document.getElementById('active-groups').textContent = stats.active_groups || 0;
              document.getElementById('machines-in-groups').textContent = stats.total_machines_in_groups || 0;
              document.getElementById('ungrouped-machines').textContent = stats.ungrouped_machines || 0;
            } catch (error) {
              console.error('Error loading statistics:', error);
            }
          }

          async function loadGroups() {
            try {
              const response = await fetch('/api/machine-groups');
              groups = await response.json();
              renderGroups();
            } catch (error) {
              console.error('Error loading groups:', error);
            }
          }

          function renderGroups() {
            const container = document.getElementById('groups-container');
            container.innerHTML = '';

            if (groups.length === 0) {
              container.innerHTML = \`
                <div class="col-span-full text-center py-12">
                  <i class="fas fa-layer-group text-4xl text-gray-400 mb-4"></i>
                  <p class="text-gray-500">Ühtegi gruppi pole veel loodud</p>
                </div>
              \`;
              return;
            }

            groups.forEach(group => {
              const card = document.createElement('div');
              card.className = 'bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow';

              card.innerHTML = \`
                <div class="flex items-center justify-between mb-4">
                  <div class="flex items-center">
                    <div class="p-2 rounded-full" style="background-color: \${group.color}20; color: \${group.color}">
                      <i class="\${group.icon} text-xl"></i>
                    </div>
                    <div class="ml-3">
                      <h3 class="font-semibold text-gray-900">\${group.name}</h3>
                      <p class="text-sm text-gray-500">\${group.machine_count} masinat</p>
                    </div>
                  </div>
                  <div class="flex space-x-2">
                    <button onclick="editGroup(\${group.id})" class="text-blue-600 hover:text-blue-800" title="Muuda">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="toggleGroupStatus(\${group.id}, \${group.is_active})"
                            class="\${group.is_active ? 'text-orange-600 hover:text-orange-800' : 'text-green-600 hover:text-green-800'}"
                            title="\${group.is_active ? 'Deaktiveeri' : 'Aktiveeri'}">
                      <i class="fas fa-\${group.is_active ? 'pause' : 'play'}"></i>
                    </button>
                    <button onclick="deleteGroup(\${group.id})" class="text-red-600 hover:text-red-800" title="Kustuta">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </div>

                \${group.description ? \`<p class="text-gray-600 text-sm mb-4">\${group.description}</p>\` : ''}

                <div class="flex items-center justify-between">
                  <span class="px-2 py-1 text-xs rounded-full \${group.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                    \${group.is_active ? 'Aktiivne' : 'Mitteaktiivne'}
                  </span>
                  <a href="/admin/machine-groups/\${group.id}/machines" class="text-blue-600 hover:text-blue-800 text-sm">
                    Vaata masinaid →
                  </a>
                </div>
              \`;

              container.appendChild(card);
            });
          }

          async function loadColorOptions() {
            try {
              const response = await fetch('/api/machine-groups/colors');
              availableColors = await response.json();
              renderColorOptions();
            } catch (error) {
              console.error('Error loading colors:', error);
            }
          }

          function renderColorOptions() {
            const container = document.getElementById('color-options');
            container.innerHTML = '';

            availableColors.forEach(color => {
              const option = document.createElement('button');
              option.type = 'button';
              option.className = 'w-8 h-8 rounded-full border-2 border-gray-300 hover:border-gray-400';
              option.style.backgroundColor = color;
              option.onclick = () => selectColor(color);
              container.appendChild(option);
            });
          }

          function selectColor(color) {
            document.getElementById('group-color').value = color;

            // Update visual selection
            document.querySelectorAll('#color-options button').forEach(btn => {
              btn.classList.remove('border-gray-800');
              btn.classList.add('border-gray-300');
            });

            event.target.classList.remove('border-gray-300');
            event.target.classList.add('border-gray-800');
          }

          async function loadIconOptions() {
            try {
              const response = await fetch('/api/machine-groups/icons');
              availableIcons = await response.json();
              renderIconOptions();
            } catch (error) {
              console.error('Error loading icons:', error);
            }
          }

          function renderIconOptions() {
            const container = document.getElementById('icon-options');
            container.innerHTML = '';

            availableIcons.forEach(icon => {
              const option = document.createElement('button');
              option.type = 'button';
              option.className = 'p-2 border border-gray-300 rounded hover:bg-gray-50 text-gray-600';
              option.innerHTML = \`<i class="\${icon}"></i>\`;
              option.onclick = () => selectIcon(icon);
              container.appendChild(option);
            });
          }

          function selectIcon(icon) {
            document.getElementById('group-icon').value = icon;

            // Update visual selection
            document.querySelectorAll('#icon-options button').forEach(btn => {
              btn.classList.remove('bg-blue-100', 'border-blue-500', 'text-blue-600');
              btn.classList.add('border-gray-300', 'text-gray-600');
            });

            event.target.classList.remove('border-gray-300', 'text-gray-600');
            event.target.classList.add('bg-blue-100', 'border-blue-500', 'text-blue-600');
          }

          function showCreateGroupModal() {
            document.getElementById('modal-title').textContent = 'Lisa Uus Grupp';
            document.getElementById('group-form').reset();
            document.getElementById('group-id').value = '';
            document.getElementById('group-color').value = '#3B82F6';
            document.getElementById('group-icon').value = 'fas fa-cogs';

            // Reset visual selections
            document.querySelectorAll('#color-options button').forEach(btn => {
              btn.classList.remove('border-gray-800');
              btn.classList.add('border-gray-300');
            });
            document.querySelectorAll('#icon-options button').forEach(btn => {
              btn.classList.remove('bg-blue-100', 'border-blue-500', 'text-blue-600');
              btn.classList.add('border-gray-300', 'text-gray-600');
            });

            document.getElementById('group-modal').classList.remove('hidden');
          }

          function hideGroupModal() {
            document.getElementById('group-modal').classList.add('hidden');
          }

          function showGroupManagement() {
            // Show advanced group management options
            const options = [
              'Masinate grupi määramine',
              'Gruppide eksport/import',
              'Gruppide statistika',
              'Gruppide arhiveerimine'
            ];

            const choice = prompt('Vali haldamise toiming:\\n' +
              options.map((opt, i) => (i + 1) + '. ' + opt).join('\\n'));

            switch(choice) {
              case '1':
                window.location.href = '/admin/machine-groups/assign';
                break;
              case '2':
                alert('Eksport/import funktsioon tuleb peagi');
                break;
              case '3':
                alert('Detailne statistika tuleb peagi');
                break;
              case '4':
                alert('Arhiveerimise funktsioon tuleb peagi');
                break;
              default:
                // User cancelled or invalid choice
                break;
            }
          }

          async function editGroup(id) {
            try {
              const response = await fetch(\`/api/machine-groups/\${id}\`);
              const group = await response.json();

              document.getElementById('modal-title').textContent = 'Muuda Gruppi';
              document.getElementById('group-id').value = group.id;
              document.getElementById('group-name').value = group.name;
              document.getElementById('group-description').value = group.description || '';
              document.getElementById('group-color').value = group.color;
              document.getElementById('group-icon').value = group.icon;

              // Update visual selections
              selectColor(group.color);
              selectIcon(group.icon);

              document.getElementById('group-modal').classList.remove('hidden');
            } catch (error) {
              console.error('Error loading group:', error);
              alert('Viga grupi andmete laadimisel');
            }
          }

          // Form submission
          document.getElementById('group-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const data = {
              name: formData.get('name'),
              description: formData.get('description'),
              color: formData.get('color'),
              icon: formData.get('icon'),
              is_active: true
            };

            const groupId = formData.get('id');
            const isEdit = groupId && groupId !== '';

            try {
              const response = await fetch(\`/api/machine-groups\${isEdit ? '/' + groupId : ''}\`, {
                method: isEdit ? 'PUT' : 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
              });

              if (response.ok) {
                hideGroupModal();
                loadGroups();
                loadStatistics();
                alert(\`Grupp \${isEdit ? 'uuendatud' : 'loodud'} edukalt!\`);
              } else {
                const error = await response.json();
                alert('Viga: ' + (error.error || 'Teadmata viga'));
              }
            } catch (error) {
              console.error('Error saving group:', error);
              alert('Viga grupi salvestamisel');
            }
          });

          async function toggleGroupStatus(id, isActive) {
            try {
              const action = isActive ? 'deactivate' : 'activate';
              const response = await fetch(\`/api/machine-groups/\${id}/\${action}\`, {
                method: 'POST'
              });

              if (response.ok) {
                loadGroups();
                loadStatistics();
                alert(\`Grupp \${isActive ? 'deaktiveeritud' : 'aktiveeritud'} edukalt!\`);
              } else {
                const error = await response.json();
                alert('Viga: ' + (error.error || 'Teadmata viga'));
              }
            } catch (error) {
              console.error('Error toggling group status:', error);
              alert('Viga grupi staatuse muutmisel');
            }
          }

          async function deleteGroup(id) {
            if (!confirm('Kas olete kindel, et soovite selle grupi kustutada?')) {
              return;
            }

            try {
              const response = await fetch(\`/api/machine-groups/\${id}\`, {
                method: 'DELETE'
              });

              if (response.ok) {
                loadGroups();
                loadStatistics();
                alert('Grupp edukalt kustutatud!');
              } else {
                const error = await response.json();
                alert('Viga: ' + (error.error || 'Teadmata viga'));
              }
            } catch (error) {
              console.error('Error deleting group:', error);
              alert('Viga grupi kustutamisel');
            }
          }
        </script>
      </body>
      </html>
    `;

    return c.html(html);
  } catch (error) {
    console.error('Error loading machine groups page:', error);
    return c.text('Error loading page', 500);
  }
});

// Reports page
pageRoutes.get('/admin/reports', async (c) => {
  try {
    const groups = await MachineGroup.findAll({ is_active: true });

    return c.html(`
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CMMS - Aruanded ja Analüütika</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
      </head>
      <body class="bg-gray-50">
        <div class="min-h-screen">
          <!-- Navigation -->
          <nav class="bg-blue-600 text-white">
            <div class="container mx-auto px-4">
              <!-- Desktop Navigation -->
              <div class="hidden md:flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                  <h1 class="text-xl font-bold">CMMS - Aruanded</h1>
                </div>
                <div class="flex space-x-4">
                  <a href="/admin" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                  </a>
                  <a href="/machines" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-cogs mr-2"></i>Masinad
                  </a>
                  <a href="/admin/machine-groups" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-layer-group mr-2"></i>Grupid
                  </a>
                  <a href="/admin/reports" class="bg-blue-800 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-chart-bar mr-2"></i>Aruanded
                  </a>
                  <a href="/admin/issues" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Rikked
                  </a>
                  <a href="/admin/maintenance" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-wrench mr-2"></i>Hooldus
                  </a>
                  <a href="/admin/partners" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-handshake mr-2"></i>Partnerid
                  </a>
                </div>
              </div>

              <!-- Mobile Navigation -->
              <div class="md:hidden">
                <div class="flex justify-between items-center py-3">
                  <h1 class="text-lg font-bold">CMMS - Aruanded</h1>
                  <button onclick="toggleMobileMenu()" class="p-2 rounded hover:bg-blue-700 min-h-[44px] min-w-[44px]">
                    <i id="mobile-menu-icon" class="fas fa-bars text-xl"></i>
                  </button>
                </div>

                <!-- Mobile Menu -->
                <div id="mobile-menu" class="hidden pb-4">
                  <div class="space-y-2">
                    <a href="/admin" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-home mr-2"></i>Dashboard
                    </a>
                    <a href="/machines" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-cogs mr-2"></i>Masinad
                    </a>
                    <a href="/admin/machine-groups" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-layer-group mr-2"></i>Grupid
                    </a>
                    <a href="/admin/reports" class="block bg-blue-800 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-chart-bar mr-2"></i>Aruanded
                    </a>
                    <a href="/admin/issues" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-exclamation-triangle mr-2"></i>Rikked
                    </a>
                    <a href="/admin/maintenance" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-wrench mr-2"></i>Hooldus
                    </a>
                    <a href="/admin/partners" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-handshake mr-2"></i>Partnerid
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </nav>

          <div class="container mx-auto px-4 py-6 sm:py-8">
            <!-- Header -->
            <div class="mb-6">
              <h2 class="text-xl sm:text-2xl font-bold text-gray-800 mb-4">📊 Aruanded ja Analüütika</h2>

              <!-- Filters -->
              <div class="bg-white rounded-lg shadow p-4 mb-6">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <label for="group-filter" class="block text-sm font-medium text-gray-700 mb-1">Grupp</label>
                    <select id="group-filter" class="w-full border border-gray-300 rounded-md px-3 py-3 text-sm min-h-[44px]">
                      <option value="">Kõik grupid</option>
                      ${groups.map(group => `
                        <option value="${group.id}">
                          ${group.name} (${group.machine_count})
                        </option>
                      `).join('')}
                    </select>
                  </div>

                  <div>
                    <label for="start-date" class="block text-sm font-medium text-gray-700 mb-1">Alguskuupäev</label>
                    <input type="date" id="start-date" class="w-full border border-gray-300 rounded-md px-3 py-3 text-sm min-h-[44px]">
                  </div>

                  <div>
                    <label for="end-date" class="block text-sm font-medium text-gray-700 mb-1">Lõppkuupäev</label>
                    <input type="date" id="end-date" class="w-full border border-gray-300 rounded-md px-3 py-3 text-sm min-h-[44px]">
                  </div>

                  <div class="flex items-end">
                    <button onclick="generateReports()" class="w-full bg-blue-600 text-white px-4 py-3 rounded hover:bg-blue-700 text-sm min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-chart-line mr-2"></i>Genereeri Aruanded
                    </button>
                  </div>
                </div>

                <!-- Export buttons (initially hidden) -->
                <div id="export-buttons" class="hidden mt-4">
                  <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-sm font-semibold text-gray-700 mb-3">📄 Ekspordi aruanded</h4>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      <div>
                        <h5 class="text-xs font-medium text-gray-600 mb-2">Gruppide analüütika</h5>
                        <div class="flex flex-wrap gap-2">
                          <div class="relative">
                            <button onclick="togglePDFDropdown()" class="bg-red-600 text-white px-3 py-2 rounded hover:bg-red-700 text-xs min-h-[40px] flex items-center">
                              <i class="fas fa-file-pdf mr-1"></i>PDF <i class="fas fa-chevron-down ml-1"></i>
                            </button>
                            <div id="pdf-dropdown" class="hidden absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded shadow-lg z-50 min-w-[120px]">
                              <button onclick="exportToPDF(false)" class="block w-full text-left px-3 py-2 text-xs text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-eye mr-1"></i>Vaata
                              </button>
                              <button onclick="exportToPDF(true)" class="block w-full text-left px-3 py-2 text-xs text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-download mr-1"></i>Lae alla
                              </button>
                            </div>
                          </div>
                          <button onclick="exportToExcel()" class="bg-green-600 text-white px-3 py-2 rounded hover:bg-green-700 text-xs min-h-[40px] flex items-center">
                            <i class="fas fa-file-excel mr-1"></i>Excel
                          </button>
                          <button onclick="printReport()" class="bg-gray-600 text-white px-3 py-2 rounded hover:bg-gray-700 text-xs min-h-[40px] flex items-center">
                            <i class="fas fa-print mr-1"></i>Prindi
                          </button>
                        </div>
                      </div>
                      <div>
                        <h5 class="text-xs font-medium text-gray-600 mb-2">Masinate jõudlus</h5>
                        <div class="flex flex-wrap gap-2">
                          <button onclick="exportMachinePerformanceToExcel()" class="bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 text-xs min-h-[40px] flex items-center">
                            <i class="fas fa-file-excel mr-1"></i>Excel
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Export Status -->
            <div id="export-status" class="hidden mb-4">
              <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
                <div class="flex items-center">
                  <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                  <span id="export-message">Ekspordi genereerimine...</span>
                </div>
              </div>
            </div>

            <!-- Loading State -->
            <div id="loading" class="hidden text-center py-8">
              <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <p class="mt-2 text-gray-600">Aruannete genereerimine...</p>
            </div>

            <!-- Reports Content -->
            <div id="reports-content" class="space-y-6">
              <!-- Initial message -->
              <div class="bg-white rounded-lg shadow p-6 text-center">
                <i class="fas fa-chart-bar text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Vali filtrid ja genereeri aruanded</h3>
                <p class="text-gray-600">Kasuta ülaltoodud filtreid, et genereerida detailsed aruanded masinate gruppide kohta.</p>
              </div>
            </div>
          </div>
        </div>

        <script>
          // Mobile menu toggle
          function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            const icon = document.getElementById('mobile-menu-icon');

            if (menu.classList.contains('hidden')) {
              menu.classList.remove('hidden');
              icon.classList.remove('fa-bars');
              icon.classList.add('fa-times');
            } else {
              menu.classList.add('hidden');
              icon.classList.remove('fa-times');
              icon.classList.add('fa-bars');
            }
          }

          // Close mobile menu when clicking outside
          document.addEventListener('click', function(event) {
            const menu = document.getElementById('mobile-menu');
            const button = event.target.closest('button[onclick="toggleMobileMenu()"]');

            if (!button && !menu.contains(event.target) && !menu.classList.contains('hidden')) {
              toggleMobileMenu();
            }
          });

          // Set default dates (last 30 days)
          document.addEventListener('DOMContentLoaded', function() {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 30);

            document.getElementById('end-date').value = endDate.toISOString().split('T')[0];
            document.getElementById('start-date').value = startDate.toISOString().split('T')[0];
          });

          // Generate reports
          async function generateReports() {
            const loading = document.getElementById('loading');
            const content = document.getElementById('reports-content');
            const groupId = document.getElementById('group-filter').value;
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;

            // Show loading
            loading.classList.remove('hidden');
            content.innerHTML = '';

            try {
              // Fetch all report data
              const [analyticsResponse, performanceResponse, trendsResponse] = await Promise.all([
                fetch('/api/reports/groups/analytics?' + new URLSearchParams({
                  ...(groupId && { group_id: groupId }),
                  ...(startDate && { start_date: startDate }),
                  ...(endDate && { end_date: endDate })
                })),
                fetch('/api/reports/machines/performance?' + new URLSearchParams({
                  ...(groupId && { group_id: groupId }),
                  ...(startDate && { start_date: startDate }),
                  ...(endDate && { end_date: endDate })
                })),
                fetch('/api/reports/trends?' + new URLSearchParams({
                  ...(groupId && { group_id: groupId }),
                  days: 30
                }))
              ]);

              const analytics = await analyticsResponse.json();
              const performance = await performanceResponse.json();
              const trends = await trendsResponse.json();

              // Hide loading
              loading.classList.add('hidden');

              // Render reports
              renderReports(analytics, performance, trends);

              // Show export buttons
              document.getElementById('export-buttons').classList.remove('hidden');

            } catch (error) {
              console.error('Error generating reports:', error);
              loading.classList.add('hidden');
              content.innerHTML = '<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">Viga aruannete genereerimisel: ' + error.message + '</div>';
            }
          }

          // Toggle PDF dropdown
          function togglePDFDropdown() {
            const dropdown = document.getElementById('pdf-dropdown');
            dropdown.classList.toggle('hidden');

            // Close dropdown when clicking outside
            document.addEventListener('click', function closeDropdown(event) {
              if (!event.target.closest('.relative')) {
                dropdown.classList.add('hidden');
                document.removeEventListener('click', closeDropdown);
              }
            });
          }

          // Export to PDF
          async function exportToPDF(download = false) {
            const exportStatus = document.getElementById('export-status');
            const exportMessage = document.getElementById('export-message');

            // Close dropdown
            document.getElementById('pdf-dropdown').classList.add('hidden');

            try {
              exportStatus.classList.remove('hidden');
              exportMessage.textContent = download ? 'PDF faili genereerimine...' : 'PDF avamine...';

              const groupId = document.getElementById('group-filter').value;
              const startDate = document.getElementById('start-date').value;
              const endDate = document.getElementById('end-date').value;

              const params = new URLSearchParams({
                ...(groupId && { group_id: groupId }),
                ...(startDate && { start_date: startDate }),
                ...(endDate && { end_date: endDate }),
                ...(download && { download: 'true' })
              });

              const url = '/api/export/groups/analytics/pdf?' + params;

              if (download) {
                // Create download link
                const link = document.createElement('a');
                link.href = url;
                link.download = 'gruppide-analyytika-' + new Date().toISOString().split('T')[0] + '.html';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                exportMessage.textContent = 'PDF fail alla laaditud!';
              } else {
                // Open PDF in new window
                window.open(url, '_blank');
                exportMessage.textContent = 'PDF avatud uues aknas!';
              }

              setTimeout(() => {
                exportStatus.classList.add('hidden');
              }, 2000);

            } catch (error) {
              console.error('Error exporting PDF:', error);
              exportMessage.textContent = 'Viga PDF genereerimisel: ' + error.message;
              setTimeout(() => {
                exportStatus.classList.add('hidden');
              }, 3000);
            }
          }

          // Export to Excel
          async function exportToExcel() {
            const exportStatus = document.getElementById('export-status');
            const exportMessage = document.getElementById('export-message');

            try {
              exportStatus.classList.remove('hidden');
              exportMessage.textContent = 'Excel faili genereerimine...';

              const groupId = document.getElementById('group-filter').value;
              const startDate = document.getElementById('start-date').value;
              const endDate = document.getElementById('end-date').value;

              const params = new URLSearchParams({
                ...(groupId && { group_id: groupId }),
                ...(startDate && { start_date: startDate }),
                ...(endDate && { end_date: endDate })
              });

              // Create download link
              const url = '/api/export/groups/analytics/excel?' + params;
              const link = document.createElement('a');
              link.href = url;
              link.download = 'gruppide-analyytika-' + new Date().toISOString().split('T')[0] + '.csv';
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);

              exportMessage.textContent = 'Excel fail alla laaditud!';
              setTimeout(() => {
                exportStatus.classList.add('hidden');
              }, 2000);

            } catch (error) {
              console.error('Error exporting Excel:', error);
              exportMessage.textContent = 'Viga Excel faili genereerimisel: ' + error.message;
              setTimeout(() => {
                exportStatus.classList.add('hidden');
              }, 3000);
            }
          }

          // Print report
          function printReport() {
            const exportStatus = document.getElementById('export-status');
            const exportMessage = document.getElementById('export-message');

            try {
              exportStatus.classList.remove('hidden');
              exportMessage.textContent = 'Printimise aken avamine...';

              const groupId = document.getElementById('group-filter').value;
              const startDate = document.getElementById('start-date').value;
              const endDate = document.getElementById('end-date').value;

              const params = new URLSearchParams({
                ...(groupId && { group_id: groupId }),
                ...(startDate && { start_date: startDate }),
                ...(endDate && { end_date: endDate })
              });

              // Open PDF in new window for printing
              const url = '/api/export/groups/analytics/pdf?' + params;
              const printWindow = window.open(url, '_blank');

              // Auto-print when loaded
              printWindow.onload = function() {
                setTimeout(() => {
                  printWindow.print();
                }, 1000);
              };

              exportMessage.textContent = 'Printimise aken avatud!';
              setTimeout(() => {
                exportStatus.classList.add('hidden');
              }, 2000);

            } catch (error) {
              console.error('Error printing report:', error);
              exportMessage.textContent = 'Viga printimise käivitamisel: ' + error.message;
              setTimeout(() => {
                exportStatus.classList.add('hidden');
              }, 3000);
            }
          }

          // Render reports
          function renderReports(analytics, performance, trends) {
            const content = document.getElementById('reports-content');

            let html = '';

            // Summary section
            if (analytics.success && analytics.summary) {
              html += renderSummarySection(analytics.summary);
            }

            // Group analytics
            if (analytics.success && analytics.data.length > 0) {
              html += renderGroupAnalytics(analytics.data);
            }

            // Machine performance
            if (performance.success && performance.data.length > 0) {
              html += renderMachinePerformance(performance.data, performance.summary);
            }

            // Trends
            if (trends.success && trends.data.length > 0) {
              html += renderTrends(trends.data);
            }

            content.innerHTML = html;

            // Initialize charts after rendering
            setTimeout(() => {
              initializeCharts(analytics, performance, trends);
            }, 100);
          }

          // Render summary section
          function renderSummarySection(summary) {
            return \`
              <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">📊 Ülevaade</h3>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">\${summary.total_groups}</div>
                    <div class="text-sm text-gray-600">Gruppi</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">\${summary.total_machines}</div>
                    <div class="text-sm text-gray-600">Masinat</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-red-600">\${summary.total_issues}</div>
                    <div class="text-sm text-gray-600">Rikked</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">\${summary.average_uptime}%</div>
                    <div class="text-sm text-gray-600">Keskmine töökindlus</div>
                  </div>
                </div>
              </div>
            \`;
          }

          // Render group analytics
          function renderGroupAnalytics(groups) {
            let html = \`
              <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">🏷️ Gruppide analüütika</h3>
                <div class="space-y-4">
            \`;

            groups.forEach(group => {
              html += \`
                <div class="border rounded-lg p-4">
                  <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                      <div class="w-4 h-4 rounded-full mr-2" style="background-color: \${group.group.color}"></div>
                      <h4 class="font-semibold">\${group.group.name}</h4>
                    </div>
                    <div class="text-sm text-gray-500">\${group.machines.total} masinat</div>
                  </div>

                  <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div class="text-center">
                      <div class="text-lg font-bold text-green-600">\${group.metrics.uptime_percentage}%</div>
                      <div class="text-xs text-gray-600">Töökindlus</div>
                    </div>
                    <div class="text-center">
                      <div class="text-lg font-bold text-blue-600">\${group.issues.total}</div>
                      <div class="text-xs text-gray-600">Rikked</div>
                    </div>
                    <div class="text-center">
                      <div class="text-lg font-bold text-purple-600">\${group.maintenance.total}</div>
                      <div class="text-xs text-gray-600">Hooldused</div>
                    </div>
                  </div>
                </div>
              \`;
            });

            html += \`
                </div>
              </div>
            \`;

            return html;
          }

          // Render machine performance
          function renderMachinePerformance(machines, summary) {
            let html = \`
              <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">⚙️ Masinate jõudlus</h3>

                <div class="mb-4 grid grid-cols-1 sm:grid-cols-3 gap-4 text-center">
                  <div>
                    <div class="text-lg font-bold text-green-600">\${summary.average_reliability}</div>
                    <div class="text-xs text-gray-600">Keskmine töökindlus</div>
                  </div>
                  <div>
                    <div class="text-lg font-bold text-blue-600">\${summary.average_maintenance_score}</div>
                    <div class="text-xs text-gray-600">Hoolduse skoor</div>
                  </div>
                  <div>
                    <div class="text-lg font-bold text-purple-600">\${summary.average_overall_score}</div>
                    <div class="text-xs text-gray-600">Üldine skoor</div>
                  </div>
                </div>

                <div class="overflow-x-auto">
                  <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                      <tr>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Masin</th>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Grupp</th>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Töökindlus</th>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Hooldus</th>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Üldine</th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
            \`;

            machines.slice(0, 10).forEach(machine => {
              const reliabilityColor = machine.scores.reliability >= 80 ? 'text-green-600' :
                                     machine.scores.reliability >= 60 ? 'text-yellow-600' : 'text-red-600';
              const maintenanceColor = machine.scores.maintenance >= 80 ? 'text-green-600' :
                                     machine.scores.maintenance >= 60 ? 'text-yellow-600' : 'text-red-600';
              const overallColor = machine.scores.overall >= 80 ? 'text-green-600' :
                                 machine.scores.overall >= 60 ? 'text-yellow-600' : 'text-red-600';

              html += \`
                <tr>
                  <td class="px-4 py-2 text-sm">
                    <div class="font-medium">\${machine.machine.name}</div>
                    <div class="text-gray-500">\${machine.machine.machine_number}</div>
                  </td>
                  <td class="px-4 py-2 text-sm">
                    <div class="flex items-center">
                      <div class="w-3 h-3 rounded-full mr-2" style="background-color: \${machine.machine.group_color}"></div>
                      <span>\${machine.machine.group_name || '-'}</span>
                    </div>
                  </td>
                  <td class="px-4 py-2 text-sm \${reliabilityColor} font-semibold">\${machine.scores.reliability}</td>
                  <td class="px-4 py-2 text-sm \${maintenanceColor} font-semibold">\${machine.scores.maintenance}</td>
                  <td class="px-4 py-2 text-sm \${overallColor} font-semibold">\${machine.scores.overall}</td>
                </tr>
              \`;
            });

            html += \`
                    </tbody>
                  </table>
                </div>
              </div>
            \`;

            return html;
          }

          // Render trends
          function renderTrends(trendsData) {
            return \`
              <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">📈 Trendid (viimased 30 päeva)</h3>
                <div class="h-64">
                  <canvas id="trendsChart"></canvas>
                </div>
              </div>
            \`;
          }

          // Initialize charts
          function initializeCharts(analytics, performance, trends) {
            // Initialize trends chart
            if (trends.success && trends.data.length > 0) {
              const ctx = document.getElementById('trendsChart');
              if (ctx) {
                new Chart(ctx, {
                  type: 'line',
                  data: {
                    labels: trends.data.map(d => d.date),
                    datasets: [
                      {
                        label: 'Rikked',
                        data: trends.data.map(d => d.issues.total),
                        borderColor: 'rgb(239, 68, 68)',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.1
                      },
                      {
                        label: 'Hooldused',
                        data: trends.data.map(d => d.maintenance.total),
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.1
                      }
                    ]
                  },
                  options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'top',
                      }
                    },
                    scales: {
                      y: {
                        beginAtZero: true
                      }
                    }
                  }
                });
              }
            }
          }

          // Export machine performance to Excel
          async function exportMachinePerformanceToExcel() {
            const exportStatus = document.getElementById('export-status');
            const exportMessage = document.getElementById('export-message');

            try {
              exportStatus.classList.remove('hidden');
              exportMessage.textContent = 'Masinate jõudluse Excel faili genereerimine...';

              const groupId = document.getElementById('group-filter').value;
              const startDate = document.getElementById('start-date').value;
              const endDate = document.getElementById('end-date').value;

              const params = new URLSearchParams({
                ...(groupId && { group_id: groupId }),
                ...(startDate && { start_date: startDate }),
                ...(endDate && { end_date: endDate })
              });

              // Create download link
              const url = '/api/export/machines/performance/excel?' + params;
              const link = document.createElement('a');
              link.href = url;
              link.download = 'masinate-joudlus-' + new Date().toISOString().split('T')[0] + '.csv';
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);

              exportMessage.textContent = 'Masinate jõudluse Excel fail alla laaditud!';
              setTimeout(() => {
                exportStatus.classList.add('hidden');
              }, 2000);

            } catch (error) {
              console.error('Error exporting machine performance Excel:', error);
              exportMessage.textContent = 'Viga masinate jõudluse Excel faili genereerimisel: ' + error.message;
              setTimeout(() => {
                exportStatus.classList.add('hidden');
              }, 3000);
            }
          }
        </script>
      </body>
      </html>
    `);
  } catch (error) {
    console.error('Error loading reports page:', error);
    return c.text('Error loading reports page', 500);
  }
});

// Parts management page
pageRoutes.get('/admin/parts', async (c) => {
  try {
    const html = `
      <!DOCTYPE html>
      <html lang="et">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Varuosade Haldus - CMMS</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
      </head>
      <body class="bg-gray-50">
        <div class="min-h-screen">
          <!-- Navigation -->
          <nav class="bg-blue-600 text-white">
            <div class="container mx-auto px-4">
              <!-- Desktop Navigation -->
              <div class="hidden md:flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                  <h1 class="text-xl font-bold">CMMS - Varuosad</h1>
                </div>
                <div class="flex space-x-4">
                  <a href="/admin" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                  </a>
                  <a href="/machines" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-cogs mr-2"></i>Masinad
                  </a>
                  <a href="/admin/machine-groups" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-layer-group mr-2"></i>Grupid
                  </a>
                  <a href="/admin/reports" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-chart-bar mr-2"></i>Aruanded
                  </a>
                  <a href="/admin/issues" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Rikked
                  </a>
                  <a href="/admin/maintenance" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-wrench mr-2"></i>Hooldus
                  </a>
                  <a href="/admin/partners" class="hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-handshake mr-2"></i>Partnerid
                  </a>
                  <a href="/admin/parts" class="bg-blue-800 px-3 py-2 rounded min-h-[44px] flex items-center">
                    <i class="fas fa-boxes mr-2"></i>Varuosad
                  </a>
                </div>
              </div>

              <!-- Mobile Navigation -->
              <div class="md:hidden">
                <div class="flex justify-between items-center py-3">
                  <h1 class="text-lg font-bold">CMMS - Varuosad</h1>
                  <button onclick="toggleMobileMenu()" class="p-2 rounded hover:bg-blue-700 min-h-[44px] min-w-[44px]">
                    <i id="mobile-menu-icon" class="fas fa-bars text-xl"></i>
                  </button>
                </div>

                <!-- Mobile Menu -->
                <div id="mobile-menu" class="hidden pb-4">
                  <div class="space-y-2">
                    <a href="/admin" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-home mr-2"></i>Dashboard
                    </a>
                    <a href="/machines" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-cogs mr-2"></i>Masinad
                    </a>
                    <a href="/admin/machine-groups" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-layer-group mr-2"></i>Grupid
                    </a>
                    <a href="/admin/reports" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-chart-bar mr-2"></i>Aruanded
                    </a>
                    <a href="/admin/issues" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-exclamation-triangle mr-2"></i>Rikked
                    </a>
                    <a href="/admin/maintenance" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-wrench mr-2"></i>Hooldus
                    </a>
                    <a href="/admin/partners" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-handshake mr-2"></i>Partnerid
                    </a>
                    <a href="/admin/parts" class="block bg-blue-800 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
                      <i class="fas fa-boxes mr-2"></i>Varuosad
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </nav>

          <div class="container mx-auto px-4 py-8">
            <!-- Header with Statistics -->
            <div class="mb-8">
              <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 mb-6">
                <h1 class="text-2xl lg:text-3xl font-bold text-gray-800">📦 Varuosade Haldus</h1>
                <div class="flex flex-col sm:flex-row gap-2">
                  <button onclick="showPartModal()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded min-h-[44px] flex items-center justify-center">
                    <i class="fas fa-plus mr-2"></i>Lisa Varuosa
                  </button>
                  <button onclick="showLowStockParts()" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-3 rounded min-h-[44px] flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Madal Varu
                  </button>
                </div>
              </div>

              <!-- Statistics Cards -->
              <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="bg-white rounded-lg shadow p-6">
                  <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                      <i class="fas fa-boxes text-xl"></i>
                    </div>
                    <div class="ml-4">
                      <p class="text-sm font-medium text-gray-600">Kokku Varuosad</p>
                      <p id="total-parts" class="text-2xl font-semibold text-gray-900">-</p>
                    </div>
                  </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                  <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                      <i class="fas fa-euro-sign text-xl"></i>
                    </div>
                    <div class="ml-4">
                      <p class="text-sm font-medium text-gray-600">Lao Väärtus</p>
                      <p id="inventory-value" class="text-2xl font-semibold text-gray-900">-</p>
                    </div>
                  </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                  <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                      <i class="fas fa-exclamation-triangle text-xl"></i>
                    </div>
                    <div class="ml-4">
                      <p class="text-sm font-medium text-gray-600">Madal Varu</p>
                      <p id="low-stock-count" class="text-2xl font-semibold text-gray-900">-</p>
                    </div>
                  </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                  <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-100 text-red-600">
                      <i class="fas fa-times-circle text-xl"></i>
                    </div>
                    <div class="ml-4">
                      <p class="text-sm font-medium text-gray-600">Otsas</p>
                      <p id="out-of-stock-count" class="text-2xl font-semibold text-gray-900">-</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Filters and Search -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
              <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Otsing</label>
                  <input type="text" id="search-input" placeholder="Otsi varuosa numbri või nime järgi..."
                         class="w-full border border-gray-300 rounded-md px-3 py-2 min-h-[44px]">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Kategooria</label>
                  <select id="category-filter" class="w-full border border-gray-300 rounded-md px-3 py-2 min-h-[44px]">
                    <option value="">Kõik kategooriad</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Laoseis</label>
                  <select id="stock-filter" class="w-full border border-gray-300 rounded-md px-3 py-2 min-h-[44px]">
                    <option value="">Kõik</option>
                    <option value="low">Madal varu</option>
                    <option value="out">Otsas</option>
                    <option value="normal">Normaalne</option>
                  </select>
                </div>
                <div class="flex items-end">
                  <button onclick="applyFilters()" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded min-h-[44px]">
                    <i class="fas fa-search mr-2"></i>Filtreeri
                  </button>
                </div>
              </div>
            </div>

            <!-- Parts Table -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
              <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">Varuosade Nimekiri</h2>
              </div>
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Varuosa</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kategooria</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Laoseis</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hind</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asukoht</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tegevused</th>
                    </tr>
                  </thead>
                  <tbody id="parts-table-body" class="bg-white divide-y divide-gray-200">
                    <!-- Parts will be loaded here -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- Part Modal -->
        <div id="part-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
              <div class="flex justify-between items-center mb-4">
                <h3 id="modal-title" class="text-lg font-medium text-gray-900">Lisa Uus Varuosa</h3>
                <button onclick="hidePartModal()" class="text-gray-400 hover:text-gray-600">
                  <i class="fas fa-times text-xl"></i>
                </button>
              </div>

              <form id="part-form" class="space-y-4">
                <input type="hidden" id="part-id" name="id">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Varuosa Number *</label>
                    <input type="text" id="part-number" name="part_number" required
                           class="w-full border border-gray-300 rounded-md px-3 py-2 min-h-[44px]">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Nimi *</label>
                    <input type="text" id="part-name" name="name" required
                           class="w-full border border-gray-300 rounded-md px-3 py-2 min-h-[44px]">
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Kirjeldus</label>
                  <textarea id="part-description" name="description" rows="3"
                            class="w-full border border-gray-300 rounded-md px-3 py-2"></textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Kategooria *</label>
                    <select id="part-category" name="category" required
                            class="w-full border border-gray-300 rounded-md px-3 py-2 min-h-[44px]">
                      <option value="">Vali kategooria</option>
                      <option value="mechanical">Mehaaniline</option>
                      <option value="electrical">Elektriline</option>
                      <option value="hydraulic">Hüdrauliline</option>
                      <option value="pneumatic">Pneumaatiline</option>
                      <option value="cooling">Jahutus</option>
                      <option value="tooling">Tööriistad</option>
                      <option value="other">Muu</option>
                    </select>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Tootja</label>
                    <input type="text" id="part-manufacturer" name="manufacturer"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 min-h-[44px]">
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Mõõtühik</label>
                    <select id="part-unit" name="unit_of_measure"
                            class="w-full border border-gray-300 rounded-md px-3 py-2 min-h-[44px]">
                      <option value="tk">tk</option>
                      <option value="m">m</option>
                      <option value="kg">kg</option>
                      <option value="l">l</option>
                      <option value="komplekt">komplekt</option>
                    </select>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Ühiku Hind (€)</label>
                    <input type="number" id="part-price" name="unit_price" step="0.01" min="0"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 min-h-[44px]">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Asukoht</label>
                    <input type="text" id="part-location" name="location"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 min-h-[44px]">
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Laokogus</label>
                    <input type="number" id="part-stock" name="quantity_in_stock" min="0"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 min-h-[44px]">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Min. Kogus</label>
                    <input type="number" id="part-min-stock" name="minimum_stock_level" min="0"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 min-h-[44px]">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Max. Kogus</label>
                    <input type="number" id="part-max-stock" name="maximum_stock_level" min="0"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 min-h-[44px]">
                  </div>
                </div>

                <div class="border-t pt-4">
                  <h4 class="text-md font-medium text-gray-900 mb-3">Sobivad Masinad</h4>
                  <div class="max-h-40 overflow-y-auto border border-gray-200 rounded-md p-3">
                    <div id="machines-list" class="space-y-2">
                      <!-- Machines will be loaded here -->
                    </div>
                  </div>
                  <p class="text-sm text-gray-500 mt-2">Vali masinad, millele see varuosa sobib</p>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                  <button type="button" onclick="hidePartModal()"
                          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 min-h-[44px]">
                    Tühista
                  </button>
                  <button type="submit"
                          class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 min-h-[44px]">
                    Salvesta
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <script>
          let parts = [];
          let categories = [];
          let machines = [];
          let selectedMachines = [];

          // Initialize page
          document.addEventListener('DOMContentLoaded', function() {
            loadParts();
            loadCategories();
            loadStatistics();
            loadMachines();
            setupEventListeners();
          });

          function setupEventListeners() {
            // Search input
            document.getElementById('search-input').addEventListener('input', debounce(applyFilters, 300));

            // Form submission
            document.getElementById('part-form').addEventListener('submit', handlePartSubmit);
          }

          function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
              const later = () => {
                clearTimeout(timeout);
                func(...args);
              };
              clearTimeout(timeout);
              timeout = setTimeout(later, wait);
            };
          }

          async function loadParts() {
            try {
              const response = await fetch('/api/parts');
              const data = await response.json();
              if (data.success) {
                parts = data.data;
                renderParts();
              }
            } catch (error) {
              console.error('Error loading parts:', error);
            }
          }

          async function loadCategories() {
            try {
              const response = await fetch('/api/parts/categories');
              const data = await response.json();
              if (data.success) {
                categories = data.data;
                renderCategoryFilter();
              }
            } catch (error) {
              console.error('Error loading categories:', error);
            }
          }

          async function loadMachines() {
            try {
              const response = await fetch('/api/parts/machines');
              const data = await response.json();
              if (data.success) {
                machines = data.data;
              }
            } catch (error) {
              console.error('Error loading machines:', error);
            }
          }

          async function loadStatistics() {
            try {
              const response = await fetch('/api/parts/statistics');
              const data = await response.json();
              if (data.success) {
                const stats = data.data;
                document.getElementById('total-parts').textContent = stats.total_parts || 0;
                document.getElementById('inventory-value').textContent = '€' + (stats.total_inventory_value || 0).toFixed(2);
                document.getElementById('low-stock-count').textContent = stats.low_stock_count || 0;
                document.getElementById('out-of-stock-count').textContent = stats.out_of_stock_count || 0;
              }
            } catch (error) {
              console.error('Error loading statistics:', error);
            }
          }

          function renderCategoryFilter() {
            const select = document.getElementById('category-filter');
            categories.forEach(cat => {
              const option = document.createElement('option');
              option.value = cat.category;
              option.textContent = \`\${cat.category} (\${cat.count})\`;
              select.appendChild(option);
            });
          }

          function renderParts() {
            const tbody = document.getElementById('parts-table-body');
            tbody.innerHTML = '';

            if (parts.length === 0) {
              tbody.innerHTML = \`
                <tr>
                  <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                    <i class="fas fa-boxes text-4xl mb-4 block"></i>
                    Varuosasid ei leitud
                  </td>
                </tr>
              \`;
              return;
            }

            parts.forEach(part => {
              const row = document.createElement('tr');
              row.className = 'hover:bg-gray-50';

              const stockStatus = getStockStatus(part);
              const stockBadge = getStockBadge(stockStatus);

              row.innerHTML = \`
                <td class="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div class="text-sm font-medium text-gray-900">\${part.name}</div>
                    <div class="text-sm text-gray-500">\${part.part_number}</div>
                    \${part.description ? \`<div class="text-xs text-gray-400 mt-1">\${part.description}</div>\` : ''}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    \${part.category}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">\${part.quantity_in_stock} \${part.unit_of_measure}</div>
                  <div class="text-xs text-gray-500">Min: \${part.minimum_stock_level}</div>
                  \${stockBadge}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  €\${parseFloat(part.unit_price || 0).toFixed(2)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  \${part.location || '-'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex space-x-2">
                    <button onclick="editPart(\${part.id})" class="text-blue-600 hover:text-blue-900" title="Muuda">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="updateStock(\${part.id})" class="text-green-600 hover:text-green-900" title="Uuenda laokogust">
                      <i class="fas fa-warehouse"></i>
                    </button>
                    <button onclick="deletePart(\${part.id})" class="text-red-600 hover:text-red-900" title="Kustuta">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              \`;

              tbody.appendChild(row);
            });
          }

          function getStockStatus(part) {
            if (part.quantity_in_stock === 0) return 'out';
            if (part.quantity_in_stock <= part.minimum_stock_level) return 'low';
            return 'normal';
          }

          function getStockBadge(status) {
            switch (status) {
              case 'out':
                return '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">Otsas</span>';
              case 'low':
                return '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">Madal</span>';
              default:
                return '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">OK</span>';
            }
          }

          function applyFilters() {
            const search = document.getElementById('search-input').value.toLowerCase();
            const category = document.getElementById('category-filter').value;
            const stockFilter = document.getElementById('stock-filter').value;

            let filteredParts = [...parts];

            if (search) {
              filteredParts = filteredParts.filter(part =>
                part.name.toLowerCase().includes(search) ||
                part.part_number.toLowerCase().includes(search) ||
                (part.description && part.description.toLowerCase().includes(search))
              );
            }

            if (category) {
              filteredParts = filteredParts.filter(part => part.category === category);
            }

            if (stockFilter) {
              filteredParts = filteredParts.filter(part => {
                const status = getStockStatus(part);
                return status === stockFilter;
              });
            }

            // Temporarily replace parts array for rendering
            const originalParts = parts;
            parts = filteredParts;
            renderParts();
            parts = originalParts;
          }

          function renderMachinesList() {
            const container = document.getElementById('machines-list');
            container.innerHTML = '';

            if (machines.length === 0) {
              container.innerHTML = '<p class="text-gray-500 text-sm">Masinaid ei leitud</p>';
              return;
            }

            machines.forEach(machine => {
              const isSelected = selectedMachines.includes(machine.id);
              const div = document.createElement('div');
              div.className = 'flex items-center';
              div.innerHTML = \`
                <input type="checkbox"
                       id="machine-\${machine.id}"
                       value="\${machine.id}"
                       \${isSelected ? 'checked' : ''}
                       onchange="toggleMachine(\${machine.id})"
                       class="mr-2 h-4 w-4 text-blue-600 border-gray-300 rounded">
                <label for="machine-\${machine.id}" class="text-sm text-gray-700 cursor-pointer">
                  \${machine.name} (\${machine.machine_number})
                  \${machine.location ? \` - \${machine.location}\` : ''}
                </label>
              \`;
              container.appendChild(div);
            });
          }

          function toggleMachine(machineId) {
            const index = selectedMachines.indexOf(machineId);
            if (index > -1) {
              selectedMachines.splice(index, 1);
            } else {
              selectedMachines.push(machineId);
            }
          }

          function showPartModal(partData = null) {
            const modal = document.getElementById('part-modal');
            const title = document.getElementById('modal-title');
            const form = document.getElementById('part-form');

            if (partData) {
              title.textContent = 'Muuda Varuosa';
              fillForm(partData);
              // Load existing machine associations
              loadPartMachines(partData.id);
            } else {
              title.textContent = 'Lisa Uus Varuosa';
              form.reset();
              document.getElementById('part-id').value = '';
              selectedMachines = [];
            }

            renderMachinesList();
            modal.classList.remove('hidden');
          }

          function hidePartModal() {
            document.getElementById('part-modal').classList.add('hidden');
          }

          async function loadPartMachines(partId) {
            try {
              const response = await fetch(\`/api/parts/\${partId}\`);
              const data = await response.json();
              if (data.success && data.data.machines) {
                selectedMachines = data.data.machines.map(m => m.machine_id);
              }
            } catch (error) {
              console.error('Error loading part machines:', error);
              selectedMachines = [];
            }
          }

          function fillForm(part) {
            document.getElementById('part-id').value = part.id;
            document.getElementById('part-number').value = part.part_number;
            document.getElementById('part-name').value = part.name;
            document.getElementById('part-description').value = part.description || '';
            document.getElementById('part-category').value = part.category;
            document.getElementById('part-manufacturer').value = part.manufacturer || '';
            document.getElementById('part-unit').value = part.unit_of_measure;
            document.getElementById('part-price').value = part.unit_price;
            document.getElementById('part-location').value = part.location || '';
            document.getElementById('part-stock').value = part.quantity_in_stock;
            document.getElementById('part-min-stock').value = part.minimum_stock_level;
            document.getElementById('part-max-stock').value = part.maximum_stock_level;
          }

          async function handlePartSubmit(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            const partId = data.id;

            // Remove empty id from data
            delete data.id;

            try {
              const url = partId ? \`/api/parts/\${partId}\` : '/api/parts';
              const method = partId ? 'PUT' : 'POST';

              const response = await fetch(url, {
                method: method,
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
              });

              const result = await response.json();

              if (result.success) {
                // Save machine associations
                await saveMachineAssociations(result.data.id);

                hidePartModal();
                loadParts();
                loadStatistics();
                alert(result.message);
              } else {
                alert('Viga: ' + result.error);
              }
            } catch (error) {
              console.error('Error saving part:', error);
              alert('Viga varuosa salvestamisel');
            }
          }

          async function saveMachineAssociations(partId) {
            try {
              // First, get existing associations
              const response = await fetch(\`/api/parts/\${partId}\`);
              const data = await response.json();
              const existingMachines = data.success && data.data.machines ?
                data.data.machines.map(m => m.machine_id) : [];

              // Remove associations that are no longer selected
              for (const machineId of existingMachines) {
                if (!selectedMachines.includes(machineId)) {
                  const association = data.data.machines.find(m => m.machine_id === machineId);
                  if (association) {
                    await fetch(\`/api/parts/machine-parts/\${association.id}\`, {
                      method: 'DELETE'
                    });
                  }
                }
              }

              // Add new associations
              for (const machineId of selectedMachines) {
                if (!existingMachines.includes(machineId)) {
                  await fetch('/api/parts/machine-parts', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                      machine_id: machineId,
                      part_id: partId,
                      quantity_needed: 1,
                      is_critical: false
                    })
                  });
                }
              }
            } catch (error) {
              console.error('Error saving machine associations:', error);
            }
          }

          async function editPart(id) {
            try {
              const response = await fetch(\`/api/parts/\${id}\`);
              const data = await response.json();
              if (data.success) {
                showPartModal(data.data);
              }
            } catch (error) {
              console.error('Error loading part:', error);
              alert('Viga varuosa andmete laadimisel');
            }
          }

          async function updateStock(id) {
            const part = parts.find(p => p.id === id);
            if (!part) return;

            const newQuantity = prompt(\`Uus laokogus varuosale "\${part.name}":\`, part.quantity_in_stock);
            if (newQuantity === null) return;

            const quantity = parseInt(newQuantity);
            if (isNaN(quantity) || quantity < 0) {
              alert('Palun sisestage kehtiv kogus');
              return;
            }

            const reason = prompt('Muutmise põhjus (valikuline):') || 'Manuaalne uuendus';

            try {
              const response = await fetch(\`/api/parts/\${id}/stock\`, {
                method: 'PUT',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({ quantity, reason })
              });

              const result = await response.json();

              if (result.success) {
                loadParts();
                loadStatistics();
                alert('Laokogus edukalt uuendatud');
              } else {
                alert('Viga: ' + result.error);
              }
            } catch (error) {
              console.error('Error updating stock:', error);
              alert('Viga laokoguse uuendamisel');
            }
          }

          async function deletePart(id) {
            const part = parts.find(p => p.id === id);
            if (!part) return;

            if (!confirm(\`Kas olete kindel, et soovite kustutada varuosa "\${part.name}"?\`)) {
              return;
            }

            try {
              const response = await fetch(\`/api/parts/\${id}\`, {
                method: 'DELETE'
              });

              const result = await response.json();

              if (result.success) {
                loadParts();
                loadStatistics();
                alert('Varuosa edukalt kustutatud');
              } else {
                alert('Viga: ' + result.error);
              }
            } catch (error) {
              console.error('Error deleting part:', error);
              alert('Viga varuosa kustutamisel');
            }
          }

          async function showLowStockParts() {
            try {
              const response = await fetch('/api/parts/low-stock');
              const data = await response.json();
              if (data.success) {
                if (data.data.length === 0) {
                  alert('Kõik varuosad on piisavas koguses!');
                  return;
                }

                // Filter current view to show only low stock parts
                document.getElementById('stock-filter').value = 'low';
                applyFilters();

                alert(\`Leitud \${data.data.length} madala varuga varuosa\`);
              }
            } catch (error) {
              console.error('Error loading low stock parts:', error);
              alert('Viga madala varuga varuosade laadimisel');
            }
          }

          // Mobile menu toggle
          function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            const icon = document.getElementById('mobile-menu-icon');

            if (menu.classList.contains('hidden')) {
              menu.classList.remove('hidden');
              icon.classList.remove('fa-bars');
              icon.classList.add('fa-times');
            } else {
              menu.classList.add('hidden');
              icon.classList.remove('fa-times');
              icon.classList.add('fa-bars');
            }
          }

          // Close mobile menu when clicking outside
          document.addEventListener('click', function(event) {
            const menu = document.getElementById('mobile-menu');
            const button = event.target.closest('button[onclick="toggleMobileMenu()"]');

            if (!button && !menu.contains(event.target) && !menu.classList.contains('hidden')) {
              toggleMobileMenu();
            }
          });
        </script>
      </body>
      </html>
    `;

    return c.html(html);
  } catch (error) {
    console.error('Error loading parts page:', error);
    return c.text('Error loading parts page', 500);
  }
});

