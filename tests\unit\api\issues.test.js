import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { app } from '../../../server.js';

describe('Issues API', () => {
  beforeEach(async () => {
    // Clean up test data
  });

  afterEach(async () => {
    // Clean up after tests
  });

  describe('POST /api/issues', () => {
    it('should create a new issue', async () => {
      // First create a test machine
      const timestamp = Date.now();
      const machineData = {
        machine_number: `M-ISSUE-${timestamp}`,
        name: 'Test Machine for Issues',
        location: 'Test Location',
        status: 'online'
      };

      const createMachineResponse = await app.request('/api/machines', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(machineData)
      });
      
      expect(createMachineResponse.status).toBe(201);
      const machine = await createMachineResponse.json();

      // Now create an issue for this machine
      const issueData = {
        machine_id: machine.id,
        operator_number: 'OP-123',
        operator_name: 'Test Operator',
        issue_type: 'mechanical',
        priority: 'high',
        title: 'Test Issue',
        description: 'This is a test issue description'
      };

      const response = await app.request('/api/issues', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(issueData)
      });

      expect(response.status).toBe(201);
      const result = await response.json();
      
      expect(result).toHaveProperty('id');
      expect(result).toHaveProperty('machine_id', machine.id);
      expect(result).toHaveProperty('operator_number', 'OP-123');
      expect(result).toHaveProperty('operator_name', 'Test Operator');
      expect(result).toHaveProperty('issue_type', 'mechanical');
      expect(result).toHaveProperty('priority', 'high');
      expect(result).toHaveProperty('title', 'Test Issue');
      expect(result).toHaveProperty('description', 'This is a test issue description');
      expect(result).toHaveProperty('status', 'open');
      expect(result).toHaveProperty('machine_number', `M-ISSUE-${timestamp}`);
    });

    it('should require required fields', async () => {
      const incompleteIssueData = {
        operator_number: 'OP-123'
        // Missing machine_id, title
      };

      const response = await app.request('/api/issues', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(incompleteIssueData)
      });

      expect(response.status).toBe(400);
      const result = await response.json();
      expect(result).toHaveProperty('error');
    });
  });

  describe('GET /api/issues', () => {
    it('should get all issues', async () => {
      const response = await app.request('/api/issues');
      
      expect(response.status).toBe(200);
      const result = await response.json();
      expect(Array.isArray(result)).toBe(true);
    });

    it('should filter issues by machine_id', async () => {
      // Create test machine and issue first
      const timestamp = Date.now();
      const machineData = {
        machine_number: `M-FILTER-${timestamp}`,
        name: 'Test Machine for Filter',
        location: 'Test Location'
      };

      const createMachineResponse = await app.request('/api/machines', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(machineData)
      });
      
      const machine = await createMachineResponse.json();

      const issueData = {
        machine_id: machine.id,
        operator_number: 'OP-FILTER',
        title: 'Filter Test Issue'
      };

      await app.request('/api/issues', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(issueData)
      });

      // Now filter by machine_id
      const response = await app.request(`/api/issues?machine_id=${machine.id}`);
      
      expect(response.status).toBe(200);
      const result = await response.json();
      expect(Array.isArray(result)).toBe(true);
      
      // All results should be for this machine
      result.forEach(issue => {
        expect(issue.machine_id).toBe(machine.id);
      });
    });
  });

  describe('GET /api/issues/:id', () => {
    it('should get issue by id', async () => {
      // Create test issue first
      const timestamp = Date.now();
      const machineData = {
        machine_number: `M-GET-${timestamp}`,
        name: 'Test Machine for GET',
        location: 'Test Location'
      };

      const createMachineResponse = await app.request('/api/machines', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(machineData)
      });
      
      const machine = await createMachineResponse.json();

      const issueData = {
        machine_id: machine.id,
        operator_number: 'OP-GET',
        title: 'GET Test Issue'
      };

      const createResponse = await app.request('/api/issues', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(issueData)
      });
      
      const issue = await createResponse.json();

      // Now get the issue by ID
      const response = await app.request(`/api/issues/${issue.id}`);
      
      expect(response.status).toBe(200);
      const result = await response.json();
      expect(result).toHaveProperty('id', issue.id);
      expect(result).toHaveProperty('title', 'GET Test Issue');
      expect(result).toHaveProperty('machine_number', `M-GET-${timestamp}`);
    });

    it('should return 404 for non-existent issue', async () => {
      const response = await app.request('/api/issues/999999');
      
      expect(response.status).toBe(404);
      const result = await response.json();
      expect(result).toHaveProperty('error', 'Issue not found');
    });
  });

  describe('PUT /api/issues/:id', () => {
    it('should update issue status', async () => {
      // Create test issue first
      const timestamp = Date.now();
      const machineData = {
        machine_number: `M-UPDATE-${timestamp}`,
        name: 'Test Machine for Update',
        location: 'Test Location'
      };

      const createMachineResponse = await app.request('/api/machines', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(machineData)
      });
      
      const machine = await createMachineResponse.json();

      const issueData = {
        machine_id: machine.id,
        operator_number: 'OP-UPDATE',
        title: 'Update Test Issue',
        status: 'open'
      };

      const createResponse = await app.request('/api/issues', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(issueData)
      });
      
      const issue = await createResponse.json();

      // Update the issue
      const updateData = {
        status: 'in_progress',
        assigned_to: 'Maintenance Team'
      };

      const response = await app.request(`/api/issues/${issue.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      });

      expect(response.status).toBe(200);
      const result = await response.json();
      expect(result).toHaveProperty('status', 'in_progress');
      expect(result).toHaveProperty('assigned_to', 'Maintenance Team');
    });
  });
});
