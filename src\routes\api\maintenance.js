import { Hono } from 'hono';
import { MaintenanceRequest } from '../../models/MaintenanceRequest.js';

export const maintenanceApiRoutes = new Hono();

// GET /api/maintenance/statistics - Get maintenance statistics (must be before /:id route)
maintenanceApiRoutes.get('/statistics', async (c) => {
  try {
    const stats = await MaintenanceRequest.getStatistics();
    return c.json(stats);
  } catch (error) {
    console.error('Error fetching maintenance statistics:', error);
    return c.json({ error: 'Failed to fetch statistics' }, 500);
  }
});

// GET /api/maintenance/upcoming - Get upcoming maintenance
maintenanceApiRoutes.get('/upcoming', async (c) => {
  try {
    const query = c.req.query();
    const days = query.days ? parseInt(query.days) : 30;
    const upcoming = await MaintenanceRequest.getUpcomingMaintenance(days);
    return c.json(upcoming);
  } catch (error) {
    console.error('Error fetching upcoming maintenance:', error);
    return c.json({ error: 'Failed to fetch upcoming maintenance' }, 500);
  }
});

// GET /api/maintenance/overdue - Get overdue maintenance
maintenanceApiRoutes.get('/overdue', async (c) => {
  try {
    const overdue = await MaintenanceRequest.getOverdueMaintenance();
    return c.json(overdue);
  } catch (error) {
    console.error('Error fetching overdue maintenance:', error);
    return c.json({ error: 'Failed to fetch overdue maintenance' }, 500);
  }
});

// POST /api/maintenance - Create a new maintenance request
maintenanceApiRoutes.post('/', async (c) => {
  try {
    const body = await c.req.json();
    
    // Validate required fields
    const { machine_id, operator_number, title } = body;
    
    if (!machine_id || !operator_number || !title) {
      return c.json({ 
        error: 'Missing required fields: machine_id, operator_number, title' 
      }, 400);
    }

    const maintenanceRequest = await MaintenanceRequest.create(body);
    return c.json(maintenanceRequest, 201);
  } catch (error) {
    console.error('Error creating maintenance request:', error);
    return c.json({ error: 'Failed to create maintenance request' }, 500);
  }
});

// GET /api/maintenance - Get all maintenance requests with optional filtering
maintenanceApiRoutes.get('/', async (c) => {
  try {
    const query = c.req.query();
    const filters = {};
    
    // Parse query parameters for filtering
    if (query.machine_id) {
      filters.machine_id = parseInt(query.machine_id);
    }
    
    if (query.status) {
      filters.status = query.status;
    }
    
    if (query.urgency) {
      filters.urgency = query.urgency;
    }
    
    if (query.maintenance_type) {
      filters.maintenance_type = query.maintenance_type;
    }
    
    if (query.limit) {
      filters.limit = parseInt(query.limit);
    }

    const maintenanceRequests = await MaintenanceRequest.findAll(filters);
    return c.json(maintenanceRequests);
  } catch (error) {
    console.error('Error fetching maintenance requests:', error);
    return c.json({ error: 'Failed to fetch maintenance requests' }, 500);
  }
});

// GET /api/maintenance/:id - Get maintenance request by ID
maintenanceApiRoutes.get('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const maintenanceRequest = await MaintenanceRequest.findById(id);
    
    if (!maintenanceRequest) {
      return c.json({ error: 'Maintenance request not found' }, 404);
    }
    
    return c.json(maintenanceRequest);
  } catch (error) {
    console.error('Error fetching maintenance request:', error);
    return c.json({ error: 'Failed to fetch maintenance request' }, 500);
  }
});

// PUT /api/maintenance/:id - Update maintenance request
maintenanceApiRoutes.put('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const updateData = await c.req.json();
    
    const maintenanceRequest = await MaintenanceRequest.update(id, updateData);
    
    if (!maintenanceRequest) {
      return c.json({ error: 'Maintenance request not found' }, 404);
    }
    
    return c.json(maintenanceRequest);
  } catch (error) {
    console.error('Error updating maintenance request:', error);
    return c.json({ error: 'Failed to update maintenance request' }, 500);
  }
});

// DELETE /api/maintenance/:id - Delete maintenance request
maintenanceApiRoutes.delete('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const deleted = await MaintenanceRequest.delete(id);
    
    if (!deleted) {
      return c.json({ error: 'Maintenance request not found' }, 404);
    }
    
    return c.json({ message: 'Maintenance request deleted successfully' });
  } catch (error) {
    console.error('Error deleting maintenance request:', error);
    return c.json({ error: 'Failed to delete maintenance request' }, 500);
  }
});
