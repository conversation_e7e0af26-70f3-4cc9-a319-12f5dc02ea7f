{"name": "cmms", "version": "1.0.0", "description": "Computerized Maintenance Management System", "main": "server.js", "scripts": {"dev": "bun run --watch server.js", "start": "bun run server.js", "test": "vitest", "test:watch": "vitest --watch", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "migrate": "bun run database/migrate.js", "seed": "bun run database/seed.js", "setup": "bash scripts/setup.sh", "setup:windows": "scripts\\setup.bat", "build": "echo 'Build step for CSS/JS optimization'"}, "dependencies": {"hono": "^3.12.0", "mysql2": "^3.6.5", "ejs": "^3.1.9", "qrcode": "^1.5.3", "multer": "^1.4.5-lts.1"}, "devDependencies": {"@playwright/test": "^1.40.0", "vitest": "^1.0.0", "@types/node": "^20.10.0"}, "type": "module"}