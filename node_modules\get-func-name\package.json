{"name": "get-func-name", "version": "2.0.2", "description": "Utility for getting a function's name for node and the browser", "keywords": ["get-func-name", "chai util"], "license": "MIT", "author": "<PERSON> <<EMAIL>> (http://alogicalparadox.com)", "contributors": ["<PERSON> (https://github.com/keithamus)", "<PERSON> (https://github.com/lucasfcosta)", "<PERSON> (https://github.com/meeber)", "<PERSON> (https://github.com/vieiralucas)", "<PERSON><PERSON><PERSON> (https://github.com/shvaikalesh)"], "files": ["index.js", "get-func-name.js"], "main": "./index.js", "repository": {"type": "git", "url": "git+ssh://**************/chaijs/get-func-name.git"}, "scripts": {"build": "browserify --bare $npm_package_main --standalone getFuncName -o get-func-name.js", "lint": "eslint --ignore-path .gitignore .", "prepublish": "npm run build", "semantic-release": "semantic-release pre && npm publish && semantic-release post", "pretest": "npm run lint", "test": "npm run test:node && npm run test:browser && npm run upload-coverage", "test:browser": "karma start --singleRun=true", "test:node": "istanbul cover _mocha", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0"}, "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "eslintConfig": {"extends": ["strict/es5"], "env": {"es6": true}, "globals": {"HTMLElement": false}, "rules": {"complexity": 0, "max-statements": 0}}, "dependencies": {}, "devDependencies": {"browserify": "^13.0.0", "browserify-istanbul": "^2.0.0", "coveralls": "2.11.14", "eslint": "^2.4.0", "eslint-config-strict": "^9.1.0", "eslint-plugin-filenames": "^1.1.0", "ghooks": "^1.0.1", "istanbul": "^0.4.2", "karma": "^1.3.0", "karma-browserify": "^5.0.2", "karma-coverage": "^1.1.1", "karma-mocha": "^1.2.0", "karma-phantomjs-launcher": "^1.0.0", "karma-sauce-launcher": "^1.0.0", "lcov-result-merger": "^1.0.2", "mocha": "^3.1.2", "phantomjs-prebuilt": "^2.1.5", "semantic-release": "^4.3.5", "simple-assert": "^1.0.0", "travis-after-all": "^1.4.4", "validate-commit-msg": "^2.3.1"}, "engines": {"node": "*"}}