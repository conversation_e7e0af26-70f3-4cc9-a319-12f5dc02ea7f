import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { app } from '../../../server.js';

describe('Machines API', () => {
  beforeEach(async () => {
    // Clean up test data
    // This will be implemented when we have the database setup
  });

  afterEach(async () => {
    // Clean up after tests
  });

  describe('POST /api/machines', () => {
    it('should create a new machine', async () => {
      const machineData = {
        machine_number: 'M-001',
        name: 'Test Freespink',
        manufacturer: 'HAAS',
        model: 'VF-2',
        department: 'Tsehh A',
        location: 'Rida 1'
      };

      const response = await app.request('/api/machines', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(machineData)
      });

      expect(response.status).toBe(201);
      const result = await response.json();
      expect(result).toHaveProperty('id');
      expect(result.machine_number).toBe('M-001');
      expect(result.name).toBe('Test Freespink');
    });

    it('should generate QR code when creating machine', async () => {
      const machineData = {
        machine_number: 'M-002',
        name: 'Test Machine 2'
      };

      const response = await app.request('/api/machines', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(machineData)
      });

      expect(response.status).toBe(201);
      const result = await response.json();
      expect(result).toHaveProperty('qr_code_generated', true);
    });
  });

  describe('GET /api/machines/:id', () => {
    it('should get machine by id', async () => {
      // This test assumes a machine with id=1 exists
      const response = await app.request('/api/machines/1');
      
      expect(response.status).toBe(200);
      const result = await response.json();
      expect(result).toHaveProperty('id', 1);
      expect(result).toHaveProperty('machine_number');
      expect(result).toHaveProperty('name');
    });
  });

  describe('PUT /api/machines/:id', () => {
    it('should update machine details', async () => {
      const updateData = {
        name: 'Uuendatud Freespink',
        location: 'Tsehh B'
      };

      const response = await app.request('/api/machines/1', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      });

      expect(response.status).toBe(200);
      const result = await response.json();
      expect(result.name).toBe('Uuendatud Freespink');
      expect(result.location).toBe('Tsehh B');
    });
  });
});
