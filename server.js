import { Hono } from 'hono';
import { serveStatic } from 'hono/bun';
import { testConnection } from './src/config/database.js';

// Import routes
import { pageRoutes } from './src/routes/pages.js';
import { machineApiRoutes } from './src/routes/api/machines.js';
import { fileRoutes } from './src/routes/api/files.js';

const app = new Hono();

// Serve static files
app.use('/public/*', serveStatic({ root: './' }));

// Page routes (HTML)
app.route('/', pageRoutes);

// API routes (JSON)
app.route('/api/machines', machineApiRoutes);
app.route('/api/files', fileRoutes);

// Health check
app.get('/health', (c) => {
  return c.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 404 handler
app.notFound((c) => {
  return c.text('Not Found', 404);
});

// Error handler
app.onError((err, c) => {
  console.error('Server error:', err);
  return c.text('Internal Server Error', 500);
});

// Start server
const port = process.env.PORT || 8080;

if (import.meta.main) {
  // Test database connection
  await testConnection();

  console.log(`🚀 CMMS Server starting on port ${port}`);

  Bun.serve({
    port,
    fetch: app.fetch,
  });
}

export { app };
